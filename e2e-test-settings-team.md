# End-to-End Test Plan: Team Management Settings Components

## Overview
This document outlines a comprehensive test plan for implementing Playwright end-to-end tests for the Team Management page components under Settings. The plan follows established test patterns from the codebase, particularly the naming conventions, structure, and language-agnostic assertion patterns from `chat-center-customer-edit-firstname.test.ts`.

## Test File Naming Convention
All test files should follow the pattern: `settings-team-[component-name]-[functionality].test.ts`

## Common Test Setup Requirements
- **Authentication**: Each test starts with login using `performLoginWithRedirectHandling(page)`
- **Navigation**: Navigate to `/settings/team` page
- **Viewport**: Use `{ width: 1920, height: 1080 }` for consistent testing
- **Language-Agnostic**: Use DOM attributes, visibility, and structure checks instead of text-based assertions
- **Round-Trip Testing**: Capture original values and revert changes to avoid test environment pollution

## Component Test Plans

### 1. UserPartner.svelte Component Tests
**File**: `src/lib/components/settings/business/UserPartner.svelte`

#### Test File: `settings-team-user-partner-crud.test.ts`

**Component Target Documentation:**
- File: `src/lib/components/settings/business/UserPartner.svelte`
- Key Elements: Lines 127-382 contain all interactive elements with `settings-team-user-partner-` prefix

**HTML Element ID Patterns:**
- Add button: `settings-team-user-partner-add-button`
- New form: `settings-team-user-partner-new-form`
- New name input: `settings-team-user-partner-new-name`
- New code input: `settings-team-user-partner-new-code`
- New color picker: `settings-team-user-partner-new-color-picker`
- New save button: `settings-team-user-partner-new-save`
- New cancel button: `settings-team-user-partner-new-cancel`
- Partner item: `settings-team-user-partner-item-{partner.id}`
- Partner name display: `settings-team-user-partner-name-{partner.id}`
- Partner code display: `settings-team-user-partner-code-{partner.id}`
- Edit button: `settings-team-user-partner-edit-button-{partner.id}`
- Edit form: `settings-team-user-partner-edit-form-{partner.id}`
- Edit name input: `settings-team-user-partner-edit-name-{partner.id}`
- Edit code input: `settings-team-user-partner-edit-code-{partner.id}`
- Edit save button: `settings-team-user-partner-edit-save-{partner.id}`
- Edit cancel button: `settings-team-user-partner-edit-cancel-{partner.id}`
- Delete button: `settings-team-user-partner-delete-button-{partner.id}`
- Delete confirm button: `settings-team-user-partner-delete-confirm-{partner.id}`
- Delete cancel button: `settings-team-user-partner-delete-cancel-{partner.id}`
- Error messages: `settings-team-user-partner-new-error`, `settings-team-user-partner-edit-error-{partner.id}`

**Test Scenarios:**
1. **Create Test**: Test creating a new partner with name, code, and color selection
2. **Edit Test**: Test modifying partner details and verifying changes are saved
3. **Delete Test**: Test deleting a partner and verifying removal from list
4. **Round-Trip Test**: Capture original state, perform operations, then revert changes

### 2. UserDepartment.svelte Component Tests
**File**: `src/lib/components/settings/business/UserDepartment.svelte`

#### Test File: `settings-team-user-department-crud.test.ts`

**Component Target Documentation:**
- File: `src/lib/components/settings/business/UserDepartment.svelte`
- Key Elements: Lines 120-471 contain all interactive elements with `settings-team-user-department-` prefix

**HTML Element ID Patterns:**
- Add button: `settings-team-user-department-add-button`
- New form: `settings-team-user-department-new-form`
- New name input: `settings-team-user-department-new-name`
- New code input: `settings-team-user-department-new-code`
- New description textarea: `settings-team-user-department-new-description`
- New color picker: `settings-team-user-department-new-color-picker`
- New save button: `settings-team-user-department-new-save`
- New cancel button: `settings-team-user-department-new-cancel`
- Department info: `settings-team-user-department-info-{department.id}`
- Department title: `settings-team-user-department-title-{department.id}`
- Department description: `settings-team-user-department-description-{department.id}`
- Edit button: `settings-team-user-department-edit-button-{department.id}`
- Edit form: `settings-team-user-department-edit-form-{department.id}`
- Edit name input: `settings-team-user-department-edit-name-{department.id}`
- Edit code input: `settings-team-user-department-edit-code-{department.id}`
- Edit description textarea: `settings-team-user-department-edit-description-{department.id}`
- Edit save button: `settings-team-user-department-edit-save-{department.id}`
- Edit cancel button: `settings-team-user-department-edit-cancel-{department.id}`
- Delete button: `settings-team-user-department-delete-button-{department.id}`
- Delete confirm button: `settings-team-user-department-delete-confirm-{department.id}`
- Delete cancel button: `settings-team-user-department-delete-cancel-{department.id}`
- Error messages: `settings-team-user-department-new-error`, `settings-team-user-department-edit-error-{department.id}`

**Test Scenarios:**
1. **Create Test**: Test creating a new department with name, code, description, and color
2. **Edit Test**: Test modifying department details including description field
3. **Delete Test**: Test deleting a department and verifying removal
4. **Round-Trip Test**: Capture original state, perform operations, then revert changes

### 3. UserTag.svelte Component Tests
**File**: `src/lib/components/settings/business/UserTag.svelte`

#### Test File: `settings-team-user-tag-crud.test.ts`

**Component Target Documentation:**
- File: `src/lib/components/settings/business/UserTag.svelte`
- Key Elements: Lines 129-356 contain all interactive elements with `settings-team-user-tag-` prefix

**HTML Element ID Patterns:**
- Add button: `settings-team-user-tag-add-button`
- New form: `settings-team-user-tag-new-form`
- New name input: `settings-team-user-tag-new-name`
- New color picker: `settings-team-user-tag-new-color-picker`
- New save button: `settings-team-user-tag-new-save`
- New cancel button: `settings-team-user-tag-new-cancel`
- Tag item: `settings-team-user-tag-item-{tag.id}` (edit mode), `settings-team-user-tag-display-{tag.id}` (display mode)
- Tag name display: `settings-team-user-tag-name-{tag.id}`
- Edit button: `settings-team-user-tag-edit-button-{tag.id}`
- Edit form: `settings-team-user-tag-edit-form-{tag.id}`
- Edit name input: `settings-team-user-tag-edit-name-{tag.id}`
- Edit save button: `settings-team-user-tag-edit-save-{tag.id}`
- Edit cancel button: `settings-team-user-tag-edit-cancel-{tag.id}`
- Delete button: `settings-team-user-tag-delete-button-{tag.id}`
- Delete confirm button: `settings-team-user-tag-delete-confirm-{tag.id}`
- Delete cancel button: `settings-team-user-tag-delete-cancel-{tag.id}`
- Error message: `settings-team-user-tag-new-error`

**Test Scenarios:**
1. **Create Test**: Test creating a new user tag with name and color selection
2. **Edit Test**: Test modifying tag name and color
3. **Delete Test**: Test deleting a tag and verifying removal
4. **Round-Trip Test**: Capture original state, perform operations, then revert changes

### 4. CustomerTag.svelte Component Tests
**File**: `src/lib/components/settings/business/CustomerTag.svelte`

#### Test File: `settings-team-customer-tag-crud.test.ts`

**Component Target Documentation:**
- File: `src/lib/components/settings/business/CustomerTag.svelte`
- Key Elements: Lines 130-363 contain all interactive elements with `settings-team-customer-tag-` prefix

**HTML Element ID Patterns:**
- Add button: `settings-team-customer-tag-add-button`
- New form: `settings-team-customer-tag-new-form`
- New name input: `settings-team-customer-tag-new-name`
- New color picker: `settings-team-customer-tag-new-color-picker`
- New save button: `settings-team-customer-tag-new-save`
- New cancel button: `settings-team-customer-tag-new-cancel`
- Tag item: `settings-team-customer-tag-item-{tag.id}`
- Tag info: `settings-team-customer-tag-info-{tag.id}`
- Tag name display: `settings-team-customer-tag-name-{tag.id}`
- Edit button: `settings-team-customer-tag-edit-button-{tag.id}`
- Edit form: `settings-team-customer-tag-edit-form-{tag.id}`
- Edit name input: `settings-team-customer-tag-edit-name-{tag.id}`
- Edit save button: `settings-team-customer-tag-edit-save-{tag.id}`
- Edit cancel button: `settings-team-customer-tag-edit-cancel-{tag.id}`
- Delete button: `settings-team-customer-tag-delete-button-{tag.id}`
- Delete confirm button: `settings-team-customer-tag-delete-confirm-{tag.id}`
- Delete cancel button: `settings-team-customer-tag-delete-cancel-{tag.id}`
- Error message: `settings-team-customer-tag-new-error`

**Test Scenarios:**
1. **Create Test**: Test creating a new customer tag with name and color selection
2. **Edit Test**: Test modifying customer tag name and color
3. **Delete Test**: Test deleting a customer tag and verifying removal
4. **Round-Trip Test**: Capture original state, perform operations, then revert changes

### 5. BusinessHour.svelte Component Tests
**File**: `src/lib/components/settings/business/BusinessHour.svelte`

#### Test File: `settings-team-business-hour-validation.test.ts`

**Component Target Documentation:**
- File: `src/lib/components/settings/business/BusinessHour.svelte`
- Key Elements: Lines 300-499 contain all interactive elements with `settings-team-business-hour-` prefix

**HTML Element ID Patterns:**
- Form: `settings-team-business-hour-form`
- Save button: `settings-team-business-hour-save`
- Day containers: `settings-team-business-hour-day-{i}` (where i is day index 0-6)
- Mobile checkboxes: `settings-team-business-hour-day-mobile-checkbox-{i}`
- Desktop checkboxes: `settings-team-business-hour-day-desktop-checkbox-{i}`
- Mobile start time selects: `settings-team-business-hour-day-mobile-start-{i}`
- Mobile end time selects: `settings-team-business-hour-day-mobile-end-{i}`
- Desktop start time selects: `settings-team-business-hour-day-desktop-start-{i}`
- Desktop end time selects: `settings-team-business-hour-day-desktop-end-{i}`
- Error alerts: `settings-team-business-hour-error`, `settings-team-business-hour-day-mobile-error-{i}`, `settings-team-business-hour-day-desktop-error-{i}`

**Test Scenarios:**
1. **Validation Test**: Test form validation for invalid time ranges (end time before start time)
2. **Save Test**: Test that valid business hour changes can be saved successfully
3. **Data Persistence Test**: Verify saved changes persist after page refresh
4. **Responsive Test**: Test both mobile and desktop layouts work correctly
5. **Round-Trip Test**: Capture original schedule, modify settings, then revert changes

## Common Test Utilities

### Authentication Utility
```typescript
import { performLoginWithRedirectHandling } from './utils/auth.utils';
```

### Navigation Utility
```typescript
async function navigateToTeamSettings(page: Page) {
    await page.goto('/settings/team');
    await expect(page).toHaveURL('/settings/team');
    await page.waitForTimeout(2000); // Allow page to load
}
```

### Round-Trip Testing Pattern
```typescript
// Capture original values
const originalValue = await inputElement.inputValue();

// Perform test operations
// ... test logic ...

// Revert changes
await inputElement.clear();
await inputElement.fill(originalValue);
await saveButton.click();
```

## Test Structure Template

Each test file should follow this structure:

1. **Imports and Setup**
2. **Utility Functions** (navigation, data capture, etc.)
3. **Test Describe Block** with component name
4. **beforeEach** hook for authentication and navigation
5. **Individual Test Cases** following the scenarios outlined above
6. **Language-Agnostic Assertions** using DOM attributes and visibility checks

## Implementation Priority

1. **Phase 1**: UserPartner and UserTag components (simpler CRUD operations)
2. **Phase 2**: UserDepartment and CustomerTag components (more complex forms)
3. **Phase 3**: BusinessHour component (validation and persistence testing)

## Notes

- All tests should use the established ID prefixing patterns to prevent DOM conflicts
- Focus on DOM attribute assertions rather than text-based checks for language independence
- Implement proper error handling and timeout configurations
- Use round-trip testing to maintain clean test environment
- Document any component-specific behaviors or edge cases discovered during implementation
