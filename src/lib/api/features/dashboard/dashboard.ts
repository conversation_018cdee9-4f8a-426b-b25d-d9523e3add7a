import { PUBLIC_BACKEND_URL, PUBLIC_GRAFANA_URL } from '$env/static/public';
import { getBackendUrl } from '$src/lib/config';

export interface DashboardData {
    id?: number;
    title?: string;
    dashboard_url?: string;
}

export async function fetchDashboard(dashboardId: number): Promise<DashboardData> {
    try {
        console.log(`${getBackendUrl()}/dashboard/api/dashboard/${dashboardId}/`);
        // const dashboardFullUrl = urlJoin(PUBLIC_BACKEND_URL, 'dashboard/api/dashboard', String(dashboardId), '/');
        // console.log('Fetching dashboard from URL:', dashboardFullUrl);
        const response = await fetch(`${getBackendUrl()}/dashboard/api/dashboard/${dashboardId}/`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        const data = await response.json();
        // console.log('Response status:', response);
        // console.log('Response data:', data);
        return data;
    } catch (error) {
        console.error('Error:', error);
        throw error; // Re-throw the error to handle it in the calling code
    }
}
