import type { Message } from '$lib/types/customer';
import { getBackendUrl } from '$src/lib/config';

export class ConversationService {
    // private baseUrl = '/api/customers';
    private baseUrl = `${getBackendUrl()}/customer/api/customers`;

    async getMessages(
        customerId: number,
        platformId: number,
        before?: number,
        limit: number = 50
    ): Promise<{
        messages: Message[];
        has_more: boolean;
    }> {
        let url = `${this.baseUrl}/${customerId}/platforms/${platformId}/messages/?limit=${limit}`;
        if (before) {
            url += `&before=${before}`;
        }

        // const response = await fetch(url);
        const response = await fetch(url, {
            credentials: 'include'
        });
        if (!response.ok) {
            throw new Error('Failed to fetch messages');
        }
        return response.json();
    }

    // async sendMessage(
    //     customerId: number,
    //     platformId: number,
    //     content: string,
    //     type: string = 'TEXT'
    // ): Promise<Message> {
    //     const response = await fetch(
    //         `${this.baseUrl}/${customerId}/platforms/${platformId}/messages/`,
    //         {
    //             method: 'POST',
    //             headers: {
    //                 'Content-Type': 'application/json',
    //             },
    //             body: JSON.stringify({
    //                 message: content,
    //                 message_type: type,
    //             }),
    //         }
    //     );

    //     if (!response.ok) {
    //         throw new Error('Failed to send message');
    //     }
    //     return response.json();
    // }

    // async markAsRead(
    //     customerId: number,
    //     platformId: number,
    //     messageIds: number[]
    // ): Promise<void> {
    //     const response = await fetch(
    //         `${this.baseUrl}/${customerId}/platforms/${platformId}/read/`,
    //         {
    //             method: 'POST',
    //             headers: {
    //                 'Content-Type': 'application/json',
    //             },
    //             body: JSON.stringify({
    //                 message_ids: messageIds,
    //             }),
    //         }
    //     );

    //     if (!response.ok) {
    //         throw new Error('Failed to mark messages as read');
    //     }
    // }

    // async uploadFile(
    //     customerId: number,
    //     platformId: number,
    //     file: File
    // ): Promise<Message> {
    //     const formData = new FormData();
    //     formData.append('file', file);
    //     formData.append('message_type', 'FILE');

    //     const response = await fetch(
    //         `${this.baseUrl}/${customerId}/platforms/${platformId}/messages/`,
    //         {
    //             method: 'POST',
    //             body: formData,
    //         }
    //     );

    //     if (!response.ok) {
    //         throw new Error('Failed to upload file');
    //     }
    //     return response.json();
    // }






    async sendMessage(
        customerId: number,
        platformId: number,
        content: string,
        type: string = 'TEXT',
        files?: File[]
    ): Promise<Message> {
        // Use FormData if we have files
        if (files && files.length > 0) {
            return this.uploadFiles(customerId, platformId, files, content);
        }
        
        // Regular JSON request for text-only messages
        const response = await fetch(
            `${this.baseUrl}/${customerId}/platforms/${platformId}/messages/`,
            {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: content,
                    message_type: type,
                }),
            }
        );

        if (!response.ok) {
            throw new Error('Failed to send message');
        }
        return response.json();
    }
    
    async uploadFiles(
        customerId: number,
        platformId: number,
        files: File[],
        message?: string
    ): Promise<Message> {
        const formData = new FormData();
        
        // Add message if provided
        if (message) {
            formData.append('message', message);
            formData.append('message_type', 'TEXT_FILE');
        } else {
            formData.append('message_type', 'FILE');
        }
        
        // Add all files
        files.forEach((file, index) => {
            formData.append(`file_${index}`, file);
        });
        
        const response = await fetch(
            `${this.baseUrl}/${customerId}/platforms/${platformId}/messages/`,
            {
                method: 'POST',
                credentials: 'include',
                body: formData
                // Don't set Content-Type - browser will set it with boundary
            }
        );
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => null);
            throw new Error(errorData?.error || 'Failed to upload files');
        }
        
        return response.json();
    }

    async markAsRead(
        customerId: number,
        platformId: number,
        messageIds: number[]
    ): Promise<void> {
        const response = await fetch(
            `${this.baseUrl}/${customerId}/platforms/${platformId}/read/`,
            {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message_ids: messageIds,
                }),
            }
        );

        if (!response.ok) {
            throw new Error('Failed to mark messages as read');
        }
    }
}

export const conversationService = new ConversationService();