// src/lib/config.ts
import { env as publicEnv } from '$env/dynamic/public';

export function getBackendUrl(): string {
  const url = publicEnv.PUBLIC_BACKEND_URL ?? 'http://localhost:8000'; // fallback!
  return url.replace(/\/$/, '');
}

export function getLiffId(): string {
  const liffId = publicEnv.PUBLIC_LIFF_ID;
  if (!liffId) {
    throw new Error('PUBLIC_LIFF_ID is not set in environment variables');
  }
  return liffId;
}