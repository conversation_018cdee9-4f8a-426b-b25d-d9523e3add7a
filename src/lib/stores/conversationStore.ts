import { writable, derived, get } from 'svelte/store';
import type { Message } from '$lib/types/customer';
import { getBackendUrl } from '$src/lib/config';

interface ConversationState {
    messages: Map<number, Message[]>; // platformId -> messages
    loadingStates: Map<number, boolean>;
    hasMore: Map<number, boolean>;
    typingIndicators: Map<number, string[]>; // platformId -> array of user names typing
}

function createConversationStore() {
    const { subscribe, set, update } = writable<ConversationState>({
        messages: new Map(),
        loadingStates: new Map(),
        hasMore: new Map(),
        typingIndicators: new Map()
    });

    return {
        subscribe,
        
        // Load initial conversation
        loadConversation: async (customerId: number, platformId: number) => {
            update(state => {
                state.loadingStates.set(platformId, true);
                return { ...state };
            });
            
            try {
                const response = await fetch(
                    // `/api/customers/${customerId}/platform/${platformId}/messages/?limit=50`
                    `${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/?limit=50`
                );
                
                if (response.ok) {
                    const data = await response.json();
                    
                    update(state => {
                        state.messages.set(platformId, data.messages || []);
                        state.hasMore.set(platformId, data.has_more || false);
                        state.loadingStates.set(platformId, false);
                        return { 
                            ...state, 
                            messages: new Map(state.messages),
                            hasMore: new Map(state.hasMore),
                            loadingStates: new Map(state.loadingStates)
                        };
                    });
                }
            } catch (error) {
                console.error('Error loading conversation:', error);
                update(state => {
                    state.loadingStates.set(platformId, false);
                    return { ...state };
                });
            }
        },
        
        // Load more messages (pagination)
        loadMoreMessages: async (customerId: number, platformId: number, beforeMessageId: number) => {
            const state = get({ subscribe });
            if (state.loadingStates.get(platformId)) return;
            
            update(state => {
                state.loadingStates.set(platformId, true);
                return { ...state };
            });
            
            try {
                const response = await fetch(
                    // `/api/customers/${customerId}/platform/${platformId}/messages/?before=${beforeMessageId}&limit=50`
                    `${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/?before=${beforeMessageId}&limit=50`
                );
                
                if (response.ok) {
                    const data = await response.json();
                    
                    update(state => {
                        const existingMessages = state.messages.get(platformId) || [];
                        const newMessages = [...(data.messages || []), ...existingMessages];
                        
                        state.messages.set(platformId, newMessages);
                        state.hasMore.set(platformId, data.has_more || false);
                        state.loadingStates.set(platformId, false);
                        
                        return { 
                            ...state, 
                            messages: new Map(state.messages),
                            hasMore: new Map(state.hasMore),
                            loadingStates: new Map(state.loadingStates)
                        };
                    });
                }
            } catch (error) {
                console.error('Error loading more messages:', error);
                update(state => {
                    state.loadingStates.set(platformId, false);
                    return { ...state };
                });
            }
        },
        
        // // Add a new message
        // addMessage: (platformId: number, message: Message) => {
        //     update(state => {
        //         const messages = state.messages.get(platformId) || [];
        //         messages.push(message);
        //         state.messages.set(platformId, messages);
        //         return { ...state, messages: new Map(state.messages) };
        //     });
        // },

        // Add a new message
        addMessage: (platformId: number, message: Message) => {
            update(state => {
                const messages = state.messages.get(platformId) || [];
                
                // Check if message already exists
                const messageExists = messages.some(m => m.id === message.id);
                if (!messageExists) {
                    messages.push(message);
                    state.messages.set(platformId, messages);
                }
                
                return { ...state, messages: new Map(state.messages) };
            });
        },
        
        // Prepend older messages
        prependMessages: (platformId: number, messages: Message[], hasMore: boolean) => {
            update(state => {
                const existingMessages = state.messages.get(platformId) || [];
                state.messages.set(platformId, [...messages, ...existingMessages]);
                state.hasMore.set(platformId, hasMore);
                return { 
                    ...state, 
                    messages: new Map(state.messages),
                    hasMore: new Map(state.hasMore)
                };
            });
        },
        
        // Update message status
        updateMessageStatus: (platformId: number, messageId: number, status: string) => {
            update(state => {
                const messages = state.messages.get(platformId);
                if (messages) {
                    const message = messages.find(m => m.id === messageId);
                    if (message) {
                        message.status = status;
                        state.messages.set(platformId, [...messages]);
                    }
                }
                return { ...state, messages: new Map(state.messages) };
            });
        },
        
        // Set typing indicator
        setTypingIndicator: (platformId: number, userId: string, isTyping: boolean) => {
            update(state => {
                const typing = state.typingIndicators.get(platformId) || [];
                
                if (isTyping && !typing.includes(userId)) {
                    typing.push(userId);
                } else if (!isTyping) {
                    const index = typing.indexOf(userId);
                    if (index > -1) typing.splice(index, 1);
                }
                
                state.typingIndicators.set(platformId, typing);
                return { ...state, typingIndicators: new Map(state.typingIndicators) };
            });
        },
        
        // Clear conversation
        clearConversation: (platformId: number) => {
            update(state => {
                state.messages.delete(platformId);
                state.loadingStates.delete(platformId);
                state.hasMore.delete(platformId);
                state.typingIndicators.delete(platformId);
                return {
                    ...state,
                    messages: new Map(state.messages),
                    loadingStates: new Map(state.loadingStates),
                    hasMore: new Map(state.hasMore),
                    typingIndicators: new Map(state.typingIndicators)
                };
            });
        },
        
        setLoadingState: (platformId: number, isLoading: boolean) => {
            update(state => {
                state.loadingStates.set(platformId, isLoading);
                return { 
                    ...state, 
                    loadingStates: new Map(state.loadingStates) 
                };
            });
        },

        // Reset all conversations
        reset: () => {
            set({
                messages: new Map(),
                loadingStates: new Map(),
                hasMore: new Map(),
                typingIndicators: new Map()
            });
        }
    };
}

export const conversationStore = createConversationStore();

// Derived stores
export const isAnyoneTyping = derived(
    conversationStore,
    $conversationStore => {
        for (const [_, typingUsers] of $conversationStore.typingIndicators) {
            if (typingUsers.length > 0) return true;
        }
        return false;
    }
);