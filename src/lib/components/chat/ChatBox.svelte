<!-- Chatbox.svelte -->
<script lang="ts">
    import { t } from '$lib/stores/i18n';
	import { onMount } from 'svelte';
	import { chatStore, messages, connectionStatus, type MessageStatus } from '$lib/stores/chatStore';
	import { chatService } from '$lib/api/features/chat/chatService';
	import ChatConnection from './ChatConnection.svelte';
	import ChatMessage from './ChatMessage.svelte';
	import ChatInput from './ChatInput.svelte';
	import LoadingSpinner from '../common/LoadingSpinner.svelte';
	import { formatMessageDate } from '$lib/utils/messageFormatter';
	import MessageItem from '../conversation/MessageItem.svelte';

	import { string } from 'zod';

	export let ticketId: string;
	export let access_token: string;
	// export let height: string = '100%';
	// export let showHeader: boolean = true;

	export let ticket: any;
	export let users: any[];
	export let priorities: any[];
	export let statuses: any[];

    export let loginUser: any;
	export let ownerUsername: string;
	export let loginUsername: string;
    export let status_id : number;
    // export let ticket_topics: any[];

	let chatConnection: ChatConnection;
	let messagesContainer: HTMLDivElement;
	let loading = false;
	let ticketDetails = null;

	const displayDate = (timestamp) => {
		const displayCreated = new Date(timestamp);

		return displayCreated.toLocaleDateString('en-GB', {
			day: '2-digit',
			month: 'short',
			year: 'numeric'
		});
	};

	// TODO - Delete this
	// console.log(`src/lib/components/chat/ChatBox.svelte's ticketId - ${ticketId}`)
	// console.log(`src/lib/components/chat/ChatBox.svelte's users - ${users}`)
	// console.log(`src/lib/components/chat/ChatBox.svelte's priorities - ${priorities}`)
	// // Auto-scroll to bottom when messages change
	// $: if (messagesContainer && $messages) {
	//   setTimeout(() => {
	//     messagesContainer.scrollTop = messagesContainer.scrollHeight;
	//   }, 0);
	// }

	onMount(async () => {
		loadTicketDetails();
	});

	async function loadTicketDetails() {
		try {
			loading = true;
			ticketDetails = await chatService.getTicketDetails(ticketId);
		} catch (error) {
			console.error('Error loading ticket details:', error);
			chatStore.setError('Failed to load ticket details');
		} finally {
			loading = false;
		}
	}

	// // Send a message
	// function handleSend(event: CustomEvent<{ message: string, type: string }>) {
	//   const { message, type } = event.detail;

	//   // TODO - Delete this
	//   console.log(`src\lib\components\chat\ChatBox.svelte's message - ${message}`)
	//   console.log(`src\lib\components\chat\ChatBox.svelte's message type - ${type}`)

	//   chatConnection.sendMessage(message, type);
	// }

	// Send a message
	async function handleSend(
		event: CustomEvent<{ access_token: string; ticketId: string; message: string; type: string }>
	) {
		const { message, type } = event.detail;

		// TODO - Delete this
		// console.log(`src/lib/components/chat/ChatBox.svelte's ticketId - ${ticketId}`);
		// console.log(`src/lib/components/chat/ChatBox.svelte's message - ${message}`);
		// console.log(`src/lib/components/chat/ChatBox.svelte's message type - ${type}`);

		//   chatConnection.sendMessage(message, type);

		try {
			// First create the message through the API
			const messageResponse = await chatService.sendMessage(access_token, ticketId, message, type);

			// TODO - Delete this
			console.log(
				`src/lib/components/chat/ChatBox.svelte's messageResponse - ${JSON.stringify(messageResponse)}`
			);

			// Second, send a message to the group
			const sourceOfMessage = 'from_webapp';
			chatConnection.sendMessage(
				message,
				type,
				sourceOfMessage,
				messageResponse.user_name,
				messageResponse.is_self,
				messageResponse.id
			);

			// Now we have a proper message ID and is_self value from the server

			// Add message to local store immediately for better UX
			chatStore.addMessage({
				id: messageResponse.id,
				// message: message,
				message: messageResponse.message,
				user_name: messageResponse.user_name,
				is_self: messageResponse.is_self,
				// message_type: type,
				message_type: messageResponse.message_type,
				status: 'SENT',
				created_on: messageResponse.created_on,
				file_url: null
			});

			// WebSocket will handle the rest (delivery confirmation, etc.)
		} catch (error) {
			console.error('Error sending message:', error);
			chatStore.setError('Failed to send message. Please try again.');
		}
	}

	// Handle file uploads
	async function handleFileSelected(event: CustomEvent<File>) {
		const file = event.detail;

		if (!file) return;

		// Determine message type based on file type
		const isImage = file.type.startsWith('image/');
		const messageType = isImage ? 'IMAGE' : 'FILE';

		try {
			// Upload the file using service
			const fileUploadResponse = await chatService.uploadFile(ticketId, file);

			// Send message with file reference
			chatConnection.sendMessage(isImage ? '[Image]' : `[File: ${file.name}]`, messageType);
		} catch (error) {
			console.error('Error uploading file:', error);
			chatStore.setError('Failed to upload file. Please try again.');
		}
	}

	// Update message status
	async function updateMessageStatus(messageId: string, newStatus: MessageStatus) {
		try {
			await chatService.updateMessageStatus(messageId, newStatus);

			// Update in local store
			chatStore.updateMessageStatus(messageId, newStatus);
		} catch (error) {
			console.error('Error updating message status:', error);
		}
	}

	// messages.subscribe((value) => {
	// 	console.log('Subscribed value:', value);
	// 	return () => console.log('Unsubscribed from store');
	// });
	// console.log(messages);

    import { Heading, Button, Dropdown, DropdownItem } from 'flowbite-svelte';
    import { DotsVerticalOutline, UserSolid, CaretLeftSolid, CaretRightSolid } from 'flowbite-svelte-icons';
    import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
    import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';
    import ChangeTicketPriority from '$lib/components/UI/ChangeTicketPriority.svelte';



	import { getStatusBadgeConfig, getPriorityBadgeConfig, groupMessagesByDate, shouldShowAvatar } from '$lib/utils';
	$: statusBadgeConfig = getStatusBadgeConfig(ticket?.status_id, ticket?.status);
	$: priorityBadgeConfig = getPriorityBadgeConfig(ticket?.priority?.name);

	$: messageGroups = groupMessagesByDate($messages);
</script>

<div class="flex h-full flex-col">
	<!-- ConversationHeader -->
	<div class="border-b border-gray-200 bg-white px-4 py-4 sm:px-6">
		<div class="flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0">
		</div>	

		<!-- Left: Avatar + Customer Info + Status Badges -->
		<div class="flex flex-col space-y-3">
			<!-- Avatar + Customer Info Row -->
			<div class="flex items-center space-x-3">
				<div class="flex min-w-0 flex-col">
					<h2 class="truncate text-lg font-semibold text-gray-900">{ticket?.customer.name}</h2>
				</div>
			</div>
		</div>

		<!-- Status Badges Row -->
		<div class="mt-3 flex w-full items-center justify-between">
			<!-- Left side badges group -->
			<div class="flex flex-wrap items-center gap-2 sm:gap-3">
				<!-- Status Badge -->
				<div
					class="flex items-center space-x-1 rounded px-3 py-1 {statusBadgeConfig.class}"
					role="status"
					aria-label="Ticket status: {statusBadgeConfig.text}"
				>
					<span class="whitespace-nowrap text-sm font-medium">
						{#if statusBadgeConfig.text !== ''}
							{t('table_status')}: {statusBadgeConfig.text}
						{/if}
					</span>
				</div>

				<!-- Priority Badge -->
				<div
					class="flex items-center space-x-1 rounded px-3 py-1 {priorityBadgeConfig.class}"
					role="status"
					aria-label="Ticket priority: {priorityBadgeConfig.text}"
				>
					<span class="whitespace-nowrap text-sm font-medium">
						{#if priorityBadgeConfig.text !== ''}
							{t('table_priority')}: {priorityBadgeConfig.text}
						{/if}
					</span>
				</div>
			</div>	
		</div>
	</div>


	<!-- Messages List -->
    <div
        bind:this={messagesContainer}
        class="flex-1 overflow-y-auto custom-scrollbar px-6 py-4 bg-gray-50"
    >

		{#if loading && $messages.length === 0}
			<div class="flex justify-center items-center h-full">
				<LoadingSpinner />
			</div>
		{:else}

			<!-- Load more indicator at top -->
			{#if loading && $messages.length > 0}
				<div class="flex justify-center py-2">
					<LoadingSpinner size="sm" />
				</div>
			{/if}

			<!-- Messages grouped by date -->
			{#each messageGroups as group}
				<!-- Date separator -->
				<div class="flex items-center my-4">
					<div class="flex-1 border-t border-gray-200"></div>
					<span class="px-3 text-xs text-gray-500 bg-gray-50">
						<!-- {group.date} -->
						{formatMessageDate(group.date)}
					</span>
					<div class="flex-1 border-t border-gray-200"></div>
				</div>

				<!-- Messages in group -->
				{#each group.messages as message, index}
					<MessageItem 
						{message}
						showAvatar={shouldShowAvatar(message, index, group.messages)}
					/>
				{/each}
			{/each}

			{#if $messages.length === 0}
				<div class="text-center text-gray-500 mt-8">
					{t('no_messages')}
				</div>
			{/if}

		{/if}
		
		<!-- {#if loading}
			<div class="flex h-full items-center justify-center">
				<div class="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
			</div>
		{:else if $messages.length === 0}
			<div class="flex h-full items-center justify-center text-gray-400">No messages yet</div>
		{:else}
			{#each $messages as message, index (message.id)}
				{#if index === 0 || displayDate(message.created_on) !== displayDate($messages[index - 1].created_on)}
					<div
						class="mx-auto my-2 w-40 rounded-md bg-neutral-700 p-2 text-center text-sm text-gray-50"
					>
						{displayDate(message.created_on)}
					</div>
				{/if}
				<ChatMessage
					id={message.id}
					message={message.message}
					userName={message.user_name}
					isSelf={message.is_self}
					status={message.status}
					timestamp={message.created_on}
					messageType={message.message_type}
					fileUrl={message.file_url}
					onUpdateStatus={updateMessageStatus}
                    message_intents={message.message_intents}
                    sub_message_intents={message.sub_message_intents}
				/>
			{/each}
		{/if} -->
	</div>

    <!-- 3 is status id of 'closed' status -->
	<!-- <ChatInput
		disabled={$connectionStatus !== 'connected' || status_id === 3} 
		on:send={handleSend}
		on:fileSelected={handleFileSelected}
		{ownerUsername}
		{loginUsername}
	/> -->
	<ChatConnection bind:this={chatConnection} {access_token} {ticketId} autoConnect={true} />
</div>
