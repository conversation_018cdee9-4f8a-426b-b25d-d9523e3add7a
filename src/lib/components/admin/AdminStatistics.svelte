<script lang="ts">
    import { UserCircleSolid } from 'flowbite-svelte-icons';
    import { t } from '$lib/stores/i18n'; 
    // Props for the component
    export let users = [];

    // Calculate statistics for the summary cards
	$: totalUsers = users?.length || 0;

    // New users (registered within the last 30 days)
	$: newUsers =
		users?.filter((user) => {
			if (!user.created_on) return false;
			const thirtyDaysAgo = new Date();
			thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
			return new Date(user.created_on) >= thirtyDaysAgo;
		}).length || 0;

	// Active users (online status)
	$: activeUsers = users?.filter((user) => user.status === 'online').length || 0;
</script>

<!-- Summary Member -->
<div class="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
    <!-- Total members -->
    <div class="flex items-center rounded-lg bg-gray-100 p-4 items-center shadow-md">
        <div class="mr-3 rounded-full bg-gray-100 p-2">
            <UserCircleSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-sm text-gray-500">{t('total_members')}</p>
            <p class="text-2xl font-bold">{totalUsers}</p>
        </div>
    </div>

    <!--New members-->
    <div class="flex items-center rounded-lg bg-gray-100 p-4 items-center shadow-md">
        <div class="mr-3 rounded-full bg-gray-100 p-2">
            <UserCircleSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-sm text-gray-500">{t('new_members_30_days')}</p>
            <p class="text-2xl font-bold">{newUsers}</p>
        </div>
    </div>

    <!--Active members-->
    <div class="flex items-center rounded-lg bg-gray-100 p-4 items-center shadow-md">
        <div class="mr-3 rounded-full bg-gray-100 p-2">
            <UserCircleSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-sm text-gray-500">{t('active_members')}</p>
            <p class="text-2xl font-bold">{activeUsers}</p>
        </div>
    </div>
</div>