<script lang="ts">
  import { t, language } from '$lib/stores/i18n';

  export let messageData = {};
  export let onAction = () => {}; // Callback for parent component
  export let isFromSelf = false; // เพิ่ม prop สำหรับบอกว่าข้อความมาจากใคร
  
  // Use provided data or show empty state
  $: data = messageData.message_type || {};

  // Event handlers
  function handleTextMessage(text) {
    console.log('Line text message:', text);
    onAction({ 
      platform: 'line', 
      type: 'text', 
      text,
      timestamp: new Date().toISOString()
    });
  }

  function handlePostback(postbackData, text) {
    console.log('Line postback action:', { data: postbackData, text });
    
    // Parse postback data
    let parsedData = {};
    if (postbackData.includes('=')) {
      const params = new URLSearchParams(postbackData.split('?')[1] || postbackData);
      const action = postbackData.split('?')[0].replace('action=', '');
      parsedData = { action, params: Object.fromEntries(params) };
    }
    
    onAction({ 
      platform: 'line', 
      type: 'postback', 
      data: postbackData,
      parsed: parsedData,
      text,
      timestamp: new Date().toISOString()
    });
  }

  function handleUri(uri, label) {
    console.log('Line URI action:', uri);
    onAction({ 
      platform: 'line', 
      type: 'uri', 
      uri,
      label,
      timestamp: new Date().toISOString()
    });
    
    // Open link in new tab
    window.open(uri, '_blank');
  }

  function handleImageMapClick(action, event) {
    event.preventDefault();
    
    if (action.type === 'message') {
      handleTextMessage(action.text);
    } else if (action.type === 'postback') {
      handlePostback(action.data, action.text || action.label);
    } else if (action.type === 'uri') {
      handleUri(action.linkUri, action.label || 'Link');
    }
  }

  function handleQuickReply(text) {
    console.log('Line quick reply:', text);
    onAction({ 
      platform: 'line', 
      type: 'quick_reply', 
      text,
      timestamp: new Date().toISOString()
    });
  }

  function calculateImageSize(baseSize, containerWidth = 400) {
    const ratio = containerWidth / baseSize.width;
    return {
      width: containerWidth,
      height: Math.round(baseSize.height * ratio)
    };
  }
</script>

<div class="line-message-renderer">
  <!-- Text Message -->
  {#if data.text}
    <div class="message-row {isFromSelf ? 'self' : 'other'}">
      <div class="text-message">
        <div class="text-bubble">
          <p>{data.text}</p>
        </div>
      </div>
    </div>
  {/if}

  <!-- Quick Reply -->
  {#if data.quick_reply && Array.isArray(data.quick_reply)}
    <div class="message-row {isFromSelf ? 'self' : 'other'}">
      <div class="quick-reply-container">
        <div class="quick-reply-buttons">
          {#each data.quick_reply as reply}
            <button
              class="quick-reply-button"
              on:click={() => handleQuickReply(reply)}
            >
              {reply}
            </button>
          {/each}
        </div>
      </div>
    </div>
  {/if}

  <!-- Image Map -->
  {#if data.image_map && data.image_map.line}
    {@const imageMap = data.image_map.line}
    {@const imageSize = calculateImageSize(imageMap.baseSize)}
    
    <div class="message-row {isFromSelf ? 'self' : 'other'}">
      <div class="image-map-container">
        <div 
          class="image-map"
          style="width: {imageSize.width}px; height: {imageSize.height}px;"
        >
          <img 
            src="{imageMap.baseUrl}" 
            alt="{imageMap.altText}"
            style="width: 100%; height: 100%; object-fit: cover;"
          />
          
          <!-- Clickable Areas -->
          {#each imageMap.actions as action, index}
            {@const scaleX = imageSize.width / imageMap.baseSize.width}
            {@const scaleY = imageSize.height / imageMap.baseSize.height}
            
            <button
              class="image-map-area"
              style="
                left: {Math.round(action.area.x * scaleX)}px;
                top: {Math.round(action.area.y * scaleY)}px;
                width: {Math.round(action.area.width * scaleX)}px;
                height: {Math.round(action.area.height * scaleY)}px;
              "
              on:click={(e) => handleImageMapClick(action, e)}
              title="{action.text || action.label || 'Click to visit link'}"
            >
              <span class="area-label">{action.text || action.label || t('visit_link')}</span>
            </button>
          {/each}
        </div>
      </div>
    </div>
  {/if}

  <!-- Image Carousel Template -->
  {#if data.image_carousel && data.image_carousel.line}
    {@const imageCarouselTemplate = data.image_carousel.line.template}
    
    <div class="message-row {isFromSelf ? 'self' : 'other'}">
      <div class="image-carousel-container">
        <div class="image-carousel-scroll">
          {#each imageCarouselTemplate.columns as column}
            <div class="image-carousel-item">
              <div 
                class="carousel-image"
                style="background-image: url({column.imageUrl});"
                on:click={() => {
                  if (column.action.type === 'uri') {
                    handleUri(column.action.uri, column.action.label);
                  } else if (column.action.type === 'message') {
                    handleTextMessage(column.action.text);
                  } else if (column.action.type === 'postback') {
                    handlePostback(column.action.data, column.action.text);
                  }
                }}
                role="button"
                tabindex="0"
                title="{column.action.label || 'Click to view'}"
              >
                <img 
                  src="{column.imageUrl}" 
                  alt="{column.action.label || 'Carousel image'}"
                  style="width: 100%; height: 100%; object-fit: cover; opacity: 0; position: absolute;"
                  on:load={(e) => e.target.style.opacity = '1'}
                  on:error={(e) => {
                    e.target.style.display = 'none';
                    e.target.parentElement.querySelector('.image-placeholder').style.opacity = '1';
                  }}
                />
                <div class="image-overlay">
                  <span class="overlay-label">{column.action.label || 'Click'}</span>
                </div>
                <div class="image-placeholder">
                  <span>{t('loading')}</span>
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    </div>
  {/if}

  <!-- Carousel Template -->
  {#if data.carousel && data.carousel.line}
    {@const carouselTemplate = data.carousel.line.template}
    {@const isMultipleColumns = carouselTemplate.columns.length > 1}
    
    <div class="message-row {isFromSelf ? 'self' : 'other'}">
      <div class="carousel-template-container {isMultipleColumns ? 'multiple' : 'single'}">
        <div class="carousel-scroll">
          {#each carouselTemplate.columns as column}
            <div class="carousel-card">
              {#if column.thumbnailImageUrl}
                <div 
                  class="card-image"
                  style="
                    background-image: url({column.thumbnailImageUrl}); 
                    background-color: {column.imageBackgroundColor || '#FFFFFF'};
                  "
                >
                  <div class="image-placeholder">
                    <span>{t('loading')}</span>
                  </div>
                </div>
              {/if}
              
              <div class="card-content">
                {#if column.title}
                  <h3 class="card-title">{column.title}</h3>
                {/if}
                {#if column.text}
                  <p class="card-text">{column.text}</p>
                {/if}
                
                {#if column.actions}
                  <div class="card-actions">
                    {#each column.actions as action}
                      <button
                        class="card-action-button"
                        on:click={() => {
                          if (action.type === 'message') {
                            handleTextMessage(action.text);
                          } else if (action.type === 'postback') {
                            handlePostback(action.data, action.text);
                          } else if (action.type === 'uri') {
                            handleUri(action.uri, action.label);
                          }
                        }}
                      >
                        {action.label}
                      </button>
                    {/each}
                  </div>
                {/if}
              </div>
            </div>
          {/each}
        </div>
      </div>
    </div>
  {/if}

  <!-- Buttons Template -->
  {#if data.buttons_template && data.buttons_template.line}
    {@const template = data.buttons_template.line.template}
    
    <div class="message-row {isFromSelf ? 'self' : 'other'}">
      <div class="buttons-template">
        {#if template.thumbnailImageUrl}
          <div 
            class="template-image"
            style="background-image: url({template.thumbnailImageUrl});"
            on:click={() => template.defaultAction && handleUri(template.defaultAction.uri, template.defaultAction.label)}
            role="button"
            tabindex="0"
          >
            <img 
              src="{template.thumbnailImageUrl}" 
              alt="{template.title || 'Template image'}"
              style="width: 100%; height: 100%; object-fit: cover; opacity: 0; position: absolute;"
              on:load={(e) => e.target.style.opacity = '1'}
              on:error={(e) => {
                e.target.style.display = 'none';
                e.target.parentElement.querySelector('.image-placeholder').style.opacity = '1';
              }}
            />
            <div class="image-placeholder">
              <span>Image placeholder</span>
            </div>
          </div>
        {/if}

        <div class="template-content">
          {#if template.title}
            <h3 class="template-title">{template.title}</h3>
          {/if}
          {#if template.text}
            <p class="template-text">{template.text}</p>
          {/if}

          <div class="template-actions">
            {#each template.actions as action}
              <button
                class="template-button {action.type}"
                on:click={() => {
                  if (action.type === 'postback') {
                    handlePostback(action.data, action.text);
                  } else if (action.type === 'uri') {
                    handleUri(action.uri, action.label);
                  }
                }}
              >
                {action.label}
              </button>
            {/each}
          </div>
        </div>
      </div>
    </div>
  {/if}

  <!-- Empty State -->
  {#if Object.keys(data).length === 0}
    <div class="message-row other">
      <div class="empty-state">
        <p>No LINE message data provided</p>
      </div>
    </div>
  {/if}
</div>

<style>
  .line-message-renderer {
    width: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* Message Row - ควบคุมการจัดตำแหน่งแต่ละแถว */
  .message-row {
    width: 100%;
    display: flex;
    margin-bottom: 16px;
    clear: both;
  }

  .message-row.self {
    justify-content: flex-end;
  }

  .message-row.other {
    justify-content: flex-start;
  }

  /* Text Message Styles */
  .text-message {
    display: inline-block;
    max-width: 400px;
  }

  .text-bubble {
    background-color: #ffffff;
    color: black;
    border-radius: 18px;
    padding: 12px 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: inline-block;
    max-width: 100%;
  }

  .text-bubble p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
  }

  /* Quick Reply Styles */
  .quick-reply-container {
    max-width: 400px;
  }

  .quick-reply-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .quick-reply-button {
    background-color: white;
    border: 1px solid #d1d5db;
    color: #374151;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .quick-reply-button:hover {
    background-color: #f9fafb;
    border-color: #06c755;
    color: #06c755;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }

  /* Image Map Styles */
  .image-map-container {
    display: inline-block;
  }

  .image-map {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .image-map-area {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-map-area:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #ffffff;
  }

  .area-label {
    background: rgba(255, 255, 255, 0.9);
    color: black;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .image-map-area:hover .area-label {
    opacity: 1;
  }

  /* Image Carousel Styles */
  .image-carousel-container {
    max-width: 100%;
    overflow: hidden;
  }

  .image-carousel-scroll {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 8px;
    scrollbar-width: thin;
    scrollbar-color: #06c755 #f0f2f5;
  }

  .image-carousel-scroll::-webkit-scrollbar {
    height: 6px;
  }

  .image-carousel-scroll::-webkit-scrollbar-track {
    background: #f0f2f5;
    border-radius: 3px;
  }

  .image-carousel-scroll::-webkit-scrollbar-thumb {
    background: #06c755;
    border-radius: 3px;
  }

  .image-carousel-item {
    flex-shrink: 0;
  }

  .carousel-image {
    width: 200px;
    height: 200px;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    border-radius: 24px;
    cursor: pointer;
    position: relative;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .carousel-image:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.4), transparent);
    padding: 12px;
    border-radius: 0 0 24px 24px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .carousel-image:hover .image-overlay {
    opacity: 1;
  }

  .overlay-label {
    color: white;
    font-size: 14px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    text-align: center;
    display: block;
  }

  /* Carousel Template Styles */
  .carousel-template-container {
    width: auto;
    max-width: 100%;
  }

  /* สำหรับ carousel ที่มี 1 column */
  .carousel-template-container.single {
    width: 280px;
    max-width: 100%;
  }

  /* สำหรับ carousel ที่มีหลาย columns */
  .carousel-template-container.multiple {
    width: 100%;
    max-width: 600px; /* กำหนดความกว้างสูงสุด */
    overflow: hidden;
  }

  .carousel-scroll {
    display: flex;
    gap: 12px;
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: 8px;
    scrollbar-width: thin;
    scrollbar-color: #06c755 #f0f2f5;
    /* เพิ่ม properties สำหรับ smooth scrolling */
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  .carousel-template-container.single .carousel-scroll {
    overflow-x: visible; /* ไม่ต้อง scroll เมื่อมี 1 card */
    justify-content: center;
  }

  .carousel-scroll::-webkit-scrollbar {
    height: 6px;
  }

  .carousel-scroll::-webkit-scrollbar-track {
    background: #f0f2f5;
    border-radius: 3px;
  }

  .carousel-scroll::-webkit-scrollbar-thumb {
    background: #06c755;
    border-radius: 3px;
  }

  .carousel-card {
    min-width: 280px;
    width: 280px; /* กำหนดความกว้างคงที่ */
    flex-shrink: 0; /* ป้องกันไม่ให้หดตัว */
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e5e9;
  }

  .card-image {
    height: 160px;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    border-radius: 8px 8px 0 0;
  }

  .card-content {
    padding: 16px;
  }

  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    line-height: 1.3;
  }

  .card-text {
    font-size: 14px;
    color: #6b7280;
    margin: 0 0 16px 0;
    line-height: 1.4;
  }

  .card-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .card-action-button {
    width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: white;
    color: #374151;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .card-action-button:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }

  /* Buttons Template Styles */
  .buttons-template {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 280px;
    max-width: 100%;
    flex-shrink: 0; /* ป้องกันไม่ให้หดตัว */
  }

  .template-image {
    height: 192px;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
    position: relative;
  }

  .image-placeholder {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f4f6;
    color: #9ca3af;
    font-size: 14px;
    opacity: 1;
    transition: opacity 0.3s ease;
  }

  .card-image[style*="background-image"] .image-placeholder,
  .template-image[style*="background-image"] .image-placeholder,
  .carousel-image[style*="background-image"] .image-placeholder {
    opacity: 0;
  }

  .template-content {
    padding: 16px;
  }

  .template-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 8px 0;
  }

  .template-text {
    color: #6b7280;
    font-size: 14px;
    margin: 0 0 16px 0;
  }

  .template-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .template-button {
    width: 100%;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .template-button.postback {
    background-color: #ffffff;
    color: black;
    border: 1px solid #e5e7eb;
  }

  .template-button.postback:hover {
    background-color: #f9fafb;
    border-color: #06c755;
    color: #06c755;
  }

  .template-button.uri {
    background-color: #3b82f6;
    color: white;
    border: 1px solid #3b82f6;
  }

  .template-button.uri:hover {
    background-color: #2563eb;
    border-color: #2563eb;
  }

  /* Empty State */
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
    background-color: #f9fafb;
    border-radius: 8px;
    border: 2px dashed #d1d5db;
    max-width: 400px;
  }

  .empty-state p {
    margin: 0;
    font-size: 14px;
  }

  /* Responsive */
  @media (max-width: 480px) {
    .message-row {
      margin-bottom: 12px;
    }
    
    .text-bubble,
    .quick-reply-container,
    .buttons-template {
      max-width: 300px;
    }
    
    .quick-reply-button {
      flex: 1;
      min-width: 0;
      text-align: center;
    }
    
    .carousel-image {
      width: 150px;
      height: 150px;
      border-radius: 20px;
    }
    
    .image-overlay {
      border-radius: 0 0 20px 20px;
    }
    
    .carousel-card {
      min-width: 250px;
      width: 250px; /* แก้ไขใน responsive */
      border-radius: 10px;
    }

    .buttons-template {
      width: 260px;
    }

    /* แก้ไข carousel template ใน responsive */
    .carousel-template-container.single {
      width: 260px;
    }

    .carousel-template-container.multiple {
      max-width: 100%;
    }
  }
</style>