<script lang="ts">
	import { DotsVerticalOutline, CloseOutline, ClockSolid } from 'flowbite-svelte-icons';
	import { Button, Dropdown, Badge } from 'flowbite-svelte';
	import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
	import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';
	import ChangeTicketPriority from '$lib/components/UI/ChangeTicketPriority.svelte';
	import { getInitials } from '$lib/utils/avatarGenerator';
	import { timeAgo } from '$lib/utils';
	import { getPriorityClass, getStatusClass } from '$lib/utils';
	import { refreshStore, triggerRefresh } from '$lib/stores/refreshStore';

	export let customerId: number;
	export let customerName: string;
	export let channelName: string;
	export let connected: boolean | null = null;
	export let platformId: any = [];
	export let ticketId: number | null = null;
	export let access_token: string;

	export let users: any[];
	export let priorities: any[];
	export let statuses: any[];
	export let topics: any[];

	import { t } from '$lib/stores/i18n';
	import { services } from '$src/lib/api/features';
	import { invalidateAll } from '$app/navigation';

	// State variables
	let ticket: any = null;
	let loginUser: any = null;
	let loading = true;
	let error: string | null = null;
	let currentPriorityName: string = ''; // Add this reactive variable
	let dropdownOpen = false;
	// Add a new state variable to track connection initialization
	let connectionInitialized = false;
	let connectionTimeout: ReturnType<typeof setTimeout> | null = null;
	let backendTicketId: string | null = null;

	// Async function to get ticket
	async function getTicket(customerId: number, platformId: any, accessToken: string) {
		try {
			loading = true;
			error = null;

			// If ticketId is provided, use it directly
			if (ticketId) {
				backendTicketId = ticketId.toString();
			} else {
				// Otherwise, get it from platform info
				const platformInfo = await services.customers.getPlatformInfo(
					customerId,
					platformId,
					accessToken
				);
				backendTicketId = platformInfo.id.toString();
			}

			// Get ticket information using the backendTicketId
			const response_ticket = await services.tickets.getById(backendTicketId, accessToken);
			const response_selfUserInfo = await services.users.getUserInfo(accessToken);

			ticket = response_ticket.tickets;
			loginUser = response_selfUserInfo.users;

			// Update the reactive variable
			currentPriorityName = ticket?.priority?.name || '';

			// For Debugging
			// console.log('ConversationHeader.svelte: getTicket(): Updated Ticket Information:', ticket);
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to fetch ticket';
			console.error('Error fetching ticket:', err);
			ticket = null;
			loginUser = null;
			currentPriorityName = '';
		} finally {
			loading = false;
		}
	}

	// Enhanced badge configuration functions
	function getStatusBadgeConfig(id: number, status: string) {
		const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
			none: {
				class: '',
				text: '',
				showIcon: false
			},
			closed: {
				class: getStatusClass(id),
				text: t('tickets_closed'),
				showIcon: true
			},
			open: {
				class: getStatusClass(id),
				text: t('tickets_open'),
				showIcon: false
			},
			assigned: {
				class: getStatusClass(id),
				text: t('tickets_assigned'),
				showIcon: false
			},
			waiting: {
				class: getStatusClass(id),
				text: t('tickets_waiting'),
				showIcon: false
			},
			pending_to_close: {
				class: getStatusClass(id),
				text: t('tickets_pending_to_close'),
				showIcon: false
			}
		};
		return configs[status?.toLowerCase()] || configs['none'];
	}

	function getPriorityBadgeConfig(priorityName: string) {
		const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
			none: {
				class: '',
				text: '',
				showIcon: false
			},
			Low: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_low'),
				showIcon: false
			},
			Medium: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_medium'),
				showIcon: true
			},
			High: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_high'),
				showIcon: true
			},
			Immediately: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_immediately'),
				showIcon: true
			}
		};
		return configs[priorityName] || configs['none'];
	}

	// Reactive function to refetch when dependencies change
	$: if (customerId && (platformId || ticketId) && access_token) {
		getTicket(customerId, platformId, access_token);
	}

	// Function to close dropdown
	function closeDropdown() {
		dropdownOpen = false;
	}

	// Function to refresh ticket data after operations
	function refreshTicketData() {
		if (customerId && (platformId || ticketId) && access_token) {
			getTicket(customerId, platformId, access_token);
			
			// Trigger global refresh to update platform identity list
			triggerRefresh();
		}
		closeDropdown();
	}

	// Reactive badge configurations
	$: statusBadgeConfig = getStatusBadgeConfig(ticket?.status_id, ticket?.status);
	$: priorityBadgeConfig = getPriorityBadgeConfig(currentPriorityName);
	// Modify the reactive statement to track when connection status is first determined
	$: if (connected !== null) {
		connectionInitialized = false;
		// Clear any existing timeout
		if (connectionTimeout) {
			clearTimeout(connectionTimeout);
		}

		// Set a minimum display time for the connecting state (500ms)
		connectionTimeout = setTimeout(() => {
			connectionInitialized = true;
		}, 500);
	}
</script>

<div id="conv-header-conversation-header" class="min-h-[125px] border-b border-gray-200 bg-white px-4 py-4 sm:px-6">
	<div class="flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0">
		<!-- Left: Avatar + Customer Info + Status Badges -->
		<div class="flex flex-col space-y-3">
			<!-- Avatar + Customer Info Row -->
			<div class="flex items-center space-x-3">
				<!-- Avatar -->
				<!-- <div class="flex-shrink-0">
					<div
						class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-500 text-sm font-semibold text-white"
						role="img"
						aria-label="Customer avatar for {customerName}"
					>
						{getInitials(customerName)}
					</div>
				</div> -->
				<!-- Customer Info -->
				<div class="flex min-w-0 flex-col">
					<h2 id="conv-header-customer-name" class="truncate text-lg font-semibold text-gray-900">{customerName}</h2>
					<p id="conv-header-channel-info" class="truncate text-sm text-gray-500">
						{t('chat_center_filter_channel')}: {channelName}
					</p>
				</div>
			</div>
		</div>

		<!-- Right: Timestamp + Actions -->
		<div
			class="flex flex-col items-start justify-end space-y-2 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0"
		>
			<!-- Last Activity -->
			<!-- {#if ticket?.updated_at}
				<div class="flex items-center text-sm text-gray-500">
					<ClockSolid class="mr-1 h-4 w-4" aria-hidden="true" />
					<span class="whitespace-nowrap"
						>Last activity: {timeAgo(ticket.updated_at, ticket?.status || '')}</span
					>
				</div>
			{/if} -->

			<!-- Actions -->
			<div class="flex items-center justify-end gap-2">
				<Button
					id="conv-header-ticket-actions-menu-button"
					color="none"
					class="rounded-lg p-2 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
					aria-label="Open ticket actions menu"
				>
					<DotsVerticalOutline class="h-5 w-5 text-gray-600" aria-hidden="true" />
				</Button>
				<Dropdown id="conv-header-ticket-actions-menu" class="w-48" placement="bottom-end" bind:open={dropdownOpen}>
					{#if ticket && !loading}
						<div id="conv-header-menu-transfer-ticket">
							<TransferTicketOwner
								ticket={{ ...ticket, id: backendTicketId }}
								{users}
								loggedInUsername={loginUser?.username}
								loggedInRole={loginUser?.roles?.[0]?.name}
								isDropDownItem={true}
								onSuccess={refreshTicketData}
							/>
						</div>
						<div id="conv-header-menu-change-status">
							<ChangeTicketStatus
								ticket={{ ...ticket, id: backendTicketId }}
								{statuses}
								ticket_topics={topics}
								isDropDownItem={true}
								onSuccess={refreshTicketData}
							/>
						</div>
						<div id="conv-header-menu-change-priority">
							<ChangeTicketPriority
								ticket={{ ...ticket, id: backendTicketId }}
								{priorities}
								isDropDownItem={true}
								onSuccess={refreshTicketData}
							/>
						</div>
					{:else}
						<!-- Show loading or disabled state when ticket data is not available -->
						<div class="px-4 py-2 text-sm text-gray-500">
							{loading ? 'Loading ticket information...' : 'Ticket information unavailable'}
						</div>
					{/if}
				</Dropdown>
			</div>
		</div>
	</div>

	<!-- Status Badges Row -->
	<div class="mt-3 flex w-full items-center justify-between">
		<!-- Left side badges group -->
		<div class="flex flex-wrap items-center gap-2 sm:gap-3">
			{#if !loading && ticket}
				<!-- Status Badge -->
				<div
					id="conv-header-badge-status"
					class="flex items-center space-x-1 rounded px-3 py-1 {statusBadgeConfig.class}"
					role="status"
					aria-label="Ticket status: {statusBadgeConfig.text}"
				>
					<span class="whitespace-nowrap text-sm font-medium">
						{#if statusBadgeConfig.text !== ''}
							{t('table_status')}: {statusBadgeConfig.text}
						{/if}
					</span>
				</div>

				<!-- Priority Badge -->
				<div
					id="conv-header-badge-priority"
					class="flex items-center space-x-1 rounded px-3 py-1 {priorityBadgeConfig.class}"
					role="status"
					aria-label="Ticket priority: {priorityBadgeConfig.text}"
				>
					<span class="whitespace-nowrap text-sm font-medium">
						{#if priorityBadgeConfig.text !== ''}
							{t('table_priority')}: {priorityBadgeConfig.text}
						{/if}
					</span>
				</div>
			{:else if loading}
				<!-- Loading state for badges -->
				<div id="conv-header-badge-loading" class="flex items-center space-x-1 rounded bg-gray-100 px-3 py-1">
					<span class="whitespace-nowrap text-sm font-medium text-gray-500">
						{t('loading')}...
					</span>
				</div>
			{/if}
			<!-- For Debugging -->
			<!-- <Badge color="purple">
				Customer ID: {customerId} | Customer Name: {customerName} | Ticket ID: {backendTicketId}
			</Badge> -->
		</div>

		<!-- Connection Badge (right-aligned) -->
		<div
			id="conv-header-badge-connection"
			class="flex items-center space-x-1 rounded border px-3 py-1 {!connectionInitialized
				? 'border-gray-500 text-gray-500'
				: connected === true
					? 'border-green-500 text-green-700'
					: 'border-red-500 text-red-700'}"
			role="status"
			aria-label="Connection status: {!connectionInitialized
				? 'initializing'
				: connected
					? t('connected')
					: t('disconnected')}"
		>
			{#if !connectionInitialized}
				<div class="h-2 w-2 animate-pulse rounded-full bg-gray-400" aria-hidden="true"></div>
				<span class="whitespace-nowrap text-sm font-medium">
					{t('connecting')}
				</span>
			{:else}
				<div
					class="h-2 w-2 animate-pulse rounded-full {connected ? 'bg-green-500' : 'bg-red-500'}"
					aria-hidden="true"
				></div>
				<span class="whitespace-nowrap text-sm font-medium">
					{connected ? t('connected') : t('disconnected')}
				</span>
			{/if}
		</div>
	</div>
</div>
