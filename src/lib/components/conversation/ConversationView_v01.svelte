<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import ConversationHeader from './ConversationHeader.svelte';
	import MessageList from './MessageList.svelte';
	import MessageInput from './MessageInput.svelte';
	import { conversationStore } from '$lib/stores/conversationStore';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';
	import type { Message } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';
	
	export let customerId: number;
	export let platformId: number;
	
	let messages: Message[] = [];
	let loading = true;
	let connected = false;
	let customerName = '';
	let channelName = '';
	
	$: platformMessages = $conversationStore.messages.get(platformId) || [];
	$: messages = platformMessages;
	
	onMount(async () => {
		await loadConversation();
		connectWebSocket();
	});
	
	onDestroy(() => {
		disconnectWebSocket();
	});
	
	async function loadConversation() {
		try {
			loading = true;
			
			// Load platform info
			// const platformResponse = await fetch(`/api/customers/${customerId}/platform-identities/${platformId}/`);
			const platformResponse = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/platform-identities/${platformId}/`, {
				// credentials: 'include', // Ensure cookies are sent for authentication
			});
			if (platformResponse.ok) {
				const platformData = await platformResponse.json();
				customerName = platformData.display_name || 'Unknown User';
				channelName = platformData.channel_name || platformData.platform;
			}
			
			// Load messages
			await conversationStore.loadConversation(customerId, platformId);
		} catch (error) {
			console.error('Error loading conversation:', error);
		} finally {
			loading = false;
		}
	}
	
	function connectWebSocket() {
		platformWebSocket.connect(customerId, platformId);
		platformWebSocket.onConnectionChange = (status) => {
			connected = status;
		};
	}
	
	function disconnectWebSocket() {
		platformWebSocket.disconnect();
	}
	
	async function handleSendMessage(event: CustomEvent<{ content: string; type: string }>) {
		const { content, type } = event.detail;
		
		try {
			// const response = await fetch(`/api/customers/${customerId}/platform/${platformId}/messages/`, {
			const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/`, {
				// credentials: 'include', // Ensure cookies are sent for authentication
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					message: content,
					message_type: type,
				}),
			});
			
			if (response.ok) {
				const newMessage = await response.json();
				conversationStore.addMessage(platformId, newMessage);
			}
		} catch (error) {
			console.error('Error sending message:', error);
		}
	}
	
	async function handleLoadMore() {
		const oldestMessage = messages[0];
		if (oldestMessage) {
			await conversationStore.loadMoreMessages(customerId, platformId, oldestMessage.id);
		}
	}
</script>

<div class="h-full flex flex-col">
	<ConversationHeader 
		{customerName}
		{channelName}
		{connected}
	/>
	
	<MessageList 
		{messages}
		{loading}
		on:loadMore={handleLoadMore}
	/>
	
	<MessageInput 
		on:send={handleSendMessage}
	/>
</div>