<script lang="ts">
    import { Checkbox, Accordion, AccordionItem } from 'flowbite-svelte';
  
    // Define insurance product categories and their subcategories
    interface SubOption {
      id: string;
      label: string;
    }
  
    interface ProductOption {
      id: string;
      label: string;
      hasSubOptions: boolean;
      subOptions?: {
        vehicleTypes?: SubOption[];
        insuranceClasses?: SubOption[];
        coverageOptions?: SubOption[];
      };
    }
  
    // Non‑life insurance product options
    const productOptions: ProductOption[] = [
      {
        id: 'vehicle',
        label: '🚗 Vehicle Insurance',
        hasSubOptions: true,
        subOptions: {
          vehicleTypes: [
            { id: 'sedan', label: 'Sedan' },
            { id: 'doubleCabPickup', label: 'Double Cab Pickup (4-door)' },
            { id: 'pickup', label: 'Pickup' },
            { id: 'van', label: 'Van' },
            { id: 'personalMotorcycle', label: 'Personal Motorcycle' },
            { id: 'commercialMotorcycle', label: 'Commercial Motorcycle' },
            { id: 'ev', label: 'Electric Vehicle (EV)' }
          ],
          insuranceClasses: [
            { id: 'class1', label: 'Class 1' },
            { id: 'class2Plus', label: 'Class 2+' },
            { id: 'class3Plus', label: 'Class 3+' },
            { id: 'class2', label: 'Class 2' },
            { id: 'class3', label: 'Class 3' }
          ]
        }
      },
      {
        id: 'compulsoryMotor',
        label: '🚗 Compulsory Motor Insurance (Por Ror Bor)',
        hasSubOptions: true,
        subOptions: {
          vehicleTypes: [
            { id: 'sedanPorRorBor', label: 'Sedan' },
            { id: 'pickupPorRorBor', label: 'Pickup' },
            { id: 'vanPorRorBor', label: 'Van' }
          ]
        }
      },
      {
        id: 'accident',
        label: '💪 Accident Insurance',
        hasSubOptions: true,
        subOptions: {
          coverageOptions: [
            { id: 'accidentOnly', label: 'Accident Only' },
            { id: 'accidentCancer', label: 'Accident & Cancer' },
            { id: 'accidentTravel', label: 'Accident & Travel' }
          ]
        }
      },
      {
        id: 'cancer',
        label: '🩻 Cancer Insurance',
        hasSubOptions: false
      },
      {
        id: 'homeProperty',
        label: '🏠 Home & Property Insurance',
        hasSubOptions: true,
        subOptions: {
          coverageOptions: [
            { id: 'generalHome', label: 'General' },
            { id: 'fireCoverage', label: 'Fire Coverage' },
            { id: 'budgetFriendly', label: 'Budget-Friendly Option' }
          ]
        }
      },
      {
        id: 'marineTransportation',
        label: '🚢 Marine & Transportation Insurance',
        hasSubOptions: true,
        subOptions: {
          coverageOptions: [
            { id: 'domesticTransportation', label: 'Domestic Transportation (please specify risks)' },
            { id: 'carrierLiability', label: 'Carrier Liability' },
            { id: 'internationalTransportation', label: 'International Transportation' }
          ]
        }
      },
      {
        id: 'businessLiability',
        label: '💼 Business & Liability Insurance',
        hasSubOptions: false
      },
      {
        id: 'cyber',
        label: '🖥️ Cyber Insurance',
        hasSubOptions: false
      },
      {
        id: 'pet',
        label: '🐶🐱 Pet Insurance',
        hasSubOptions: false
      }
    ];
  
    // Life insurance options defined separately
    const lifeInsuranceOptions = {
      id: 'lifeInsurance',
      label: 'Life Insurance',
      hasSubOptions: true,
      subOptions: {
        coverageOptions: [
          { id: 'wholeLife', label: 'Whole Life' },
          { id: 'termLife', label: 'Term Life' },
          { id: 'endowment', label: 'Endowment' },
          { id: 'annuity', label: 'Annuity' }
        ]
      }
    };
  
    // State management for non‑life insurance
    let selectedProducts: string[] = [];
    let selectedSubOptions: { [key: string]: { [subType: string]: string[] } } = {};
  
    // State management for life insurance
    let lifeInsuranceSelected = false;
    let selectedLifeInsuranceOptions: string[] = [];
  
    // Track if any changes have been made
    let hasUnsavedChanges = false;
  
    // Function to toggle non‑life product selection
    function toggleProduct(productId: string) {
      if (selectedProducts.includes(productId)) {
        selectedProducts = selectedProducts.filter(id => id !== productId);
        if (selectedSubOptions[productId]) {
          delete selectedSubOptions[productId];
        }
      } else {
        selectedProducts = [...selectedProducts, productId];
        const product = productOptions.find(p => p.id === productId);
        if (product?.hasSubOptions) {
          selectedSubOptions[productId] = {};
          if (product.subOptions?.vehicleTypes) {
            selectedSubOptions[productId]['vehicleTypes'] = [];
          }
          if (product.subOptions?.insuranceClasses) {
            selectedSubOptions[productId]['insuranceClasses'] = [];
          }
          if (product.subOptions?.coverageOptions) {
            selectedSubOptions[productId]['coverageOptions'] = [];
          }
        }
      }
      markUnsaved();
    }
  
    // Function to toggle non‑life sub‑option selection
    function toggleSubOption(productId: string, subType: string, optionId: string) {
      if (!selectedSubOptions[productId]) {
        selectedSubOptions[productId] = {};
      }
      if (!selectedSubOptions[productId][subType]) {
        selectedSubOptions[productId][subType] = [];
      }
      if (selectedSubOptions[productId][subType].includes(optionId)) {
        selectedSubOptions[productId][subType] = selectedSubOptions[productId][subType].filter(id => id !== optionId);
      } else {
        selectedSubOptions[productId][subType] = [...selectedSubOptions[productId][subType], optionId];
      }
      selectedSubOptions = { ...selectedSubOptions };
      markUnsaved();
    }
  
    // Function to toggle life insurance selection
    function toggleLifeInsurance() {
      lifeInsuranceSelected = !lifeInsuranceSelected;
      if (!lifeInsuranceSelected) {
        selectedLifeInsuranceOptions = [];
      }
      markUnsaved();
    }
  
    // Function to toggle life insurance option selection
    function toggleLifeInsuranceOption(optionId: string) {
      if (selectedLifeInsuranceOptions.includes(optionId)) {
        selectedLifeInsuranceOptions = selectedLifeInsuranceOptions.filter(id => id !== optionId);
      } else {
        selectedLifeInsuranceOptions = [...selectedLifeInsuranceOptions, optionId];
      }
      markUnsaved();
    }
  
    function markUnsaved() {
      hasUnsavedChanges = true;
    }
  
    // Save settings and reset the form
    let savedSettings: {
      products: {
        productName: string;
        subOptions: {
          category: string;
          options: string[];
        }[];
      }[];
    }[] = [];
  
    function saveChanges() {
      const productsData = selectedProducts.map(productId => {
        const product = productOptions.find(p => p.id === productId);
        const subOptionsData = [];
  
        if (product?.hasSubOptions && selectedSubOptions[productId]) {
          if (selectedSubOptions[productId]['vehicleTypes']?.length > 0) {
            const vehicleTypeLabels = selectedSubOptions[productId]['vehicleTypes'].map(vtId => {
              return product.subOptions?.vehicleTypes?.find(vt => vt.id === vtId)?.label || '';
            }).filter(label => label !== '');
            subOptionsData.push({
              category: 'Vehicle Types',
              options: vehicleTypeLabels
            });
          }
          if (selectedSubOptions[productId]['insuranceClasses']?.length > 0) {
            const classLabels = selectedSubOptions[productId]['insuranceClasses'].map(cId => {
              return product.subOptions?.insuranceClasses?.find(c => c.id === cId)?.label || '';
            }).filter(label => label !== '');
            subOptionsData.push({
              category: 'Insurance Classes',
              options: classLabels
            });
          }
          if (selectedSubOptions[productId]['coverageOptions']?.length > 0) {
            const coverageLabels = selectedSubOptions[productId]['coverageOptions'].map(coId => {
              return product.subOptions?.coverageOptions?.find(co => co.id === coId)?.label || '';
            }).filter(label => label !== '');
            subOptionsData.push({
              category: 'Coverage Options',
              options: coverageLabels
            });
          }
        }
        return {
          productName: product?.label || '',
          subOptions: subOptionsData
        };
      });
  
      // Append life insurance data if selected
      if (lifeInsuranceSelected) {
        const lifeSubOptions =
          selectedLifeInsuranceOptions.length > 0
            ? [{
                category: 'Coverage Options',
                options: selectedLifeInsuranceOptions
                  .map(optionId =>
                    lifeInsuranceOptions.subOptions?.coverageOptions?.find(opt => opt.id === optionId)?.label || ''
                  )
                  .filter(label => label !== '')
              }]
            : [];
        productsData.push({
          productName: lifeInsuranceOptions.label,
          subOptions: lifeSubOptions
        });
      }
  
      savedSettings = [...savedSettings, { products: productsData }];
  
      // Reset form
      selectedProducts = [];
      selectedSubOptions = {};
      lifeInsuranceSelected = false;
      selectedLifeInsuranceOptions = [];
      hasUnsavedChanges = false;
    }
  </script>
  
  <!-- Main Form Container -->
  <div class="p-6 bg-gray-50 rounded-lg space-y-6">
    <!-- Header Row with Save Button -->
    <div class="flex justify-between items-center">
      <h2 class="text-xl font-bold">Insurance Product Selection</h2>
      <button 
        type="button"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm 
          {hasUnsavedChanges ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-gray-400 bg-gray-100 cursor-not-allowed'}"
        on:click={saveChanges}
        disabled={!hasUnsavedChanges}
      >
        {#if hasUnsavedChanges}
          Save Changes
        {:else}
          Save
        {/if}
      </button>
    </div>
  
    <!-- Instructions -->
    <div class="bg-blue-50 p-3 rounded-lg border border-blue-200">
      <p class="text-blue-800">
        Select all insurance products that should be available. For products with additional options, you can specify which specific types or coverages to include.
      </p>
    </div>
  
    <!-- Outer Accordion wrapping both categories -->
    <Accordion>
      <!-- Non‑Life Insurance Accordion Item -->
      <AccordionItem>
        <span slot="header">Non‑Life Insurance</span>
        <div class="space-y-4 mt-4">
          {#each productOptions as product}
            <div class="border border-gray-200 rounded-lg bg-white overflow-hidden">
              <!-- Product Checkbox -->
              <div class="p-4 flex items-center border-b border-gray-200">
                <Checkbox 
                  checked={selectedProducts.includes(product.id)} 
                  on:change={() => toggleProduct(product.id)}
                >
                  <span class="font-medium">{product.label}</span>
                </Checkbox>
              </div>
              <!-- Sub-options (if applicable and selected) -->
              {#if product.hasSubOptions && selectedProducts.includes(product.id)}
                <div class="border-t border-gray-100">
                  <Accordion flush>
                    {#if product.subOptions?.vehicleTypes}
                      <AccordionItem>
                        <span slot="header">Vehicle Types</span>
                        <div class="p-4 grid grid-cols-1 md:grid-cols-2 gap-2">
                          {#each product.subOptions.vehicleTypes as vehicleType}
                            <Checkbox 
                              checked={selectedSubOptions[product.id]?.['vehicleTypes']?.includes(vehicleType.id) || false}
                              on:change={() => toggleSubOption(product.id, 'vehicleTypes', vehicleType.id)}
                            >
                              {vehicleType.label}
                            </Checkbox>
                          {/each}
                        </div>
                      </AccordionItem>
                    {/if}
                    {#if product.subOptions?.insuranceClasses}
                      <AccordionItem>
                        <span slot="header">Insurance Classes</span>
                        <div class="p-4 grid grid-cols-1 md:grid-cols-2 gap-2">
                          {#each product.subOptions.insuranceClasses as insuranceClass}
                            <Checkbox 
                              checked={selectedSubOptions[product.id]?.['insuranceClasses']?.includes(insuranceClass.id) || false}
                              on:change={() => toggleSubOption(product.id, 'insuranceClasses', insuranceClass.id)}
                            >
                              {insuranceClass.label}
                            </Checkbox>
                          {/each}
                        </div>
                      </AccordionItem>
                    {/if}
                    {#if product.subOptions?.coverageOptions}
                      <AccordionItem>
                        <span slot="header">Coverage Options</span>
                        <div class="p-4 grid grid-cols-1 md:grid-cols-2 gap-2">
                          {#each product.subOptions.coverageOptions as coverageOption}
                            <Checkbox 
                              checked={selectedSubOptions[product.id]?.['coverageOptions']?.includes(coverageOption.id) || false}
                              on:change={() => toggleSubOption(product.id, 'coverageOptions', coverageOption.id)}
                            >
                              {coverageOption.label}
                            </Checkbox>
                          {/each}
                        </div>
                      </AccordionItem>
                    {/if}
                  </Accordion>
                </div>
              {/if}
            </div>
          {/each}
        </div>
      </AccordionItem>
  
      <!-- Life Insurance Accordion Item -->
      <AccordionItem>
        <span slot="header">Life Insurance</span>
        <div class="mt-4">
          <div class="border border-gray-200 rounded-lg bg-white overflow-hidden">
            <!-- Life Insurance Checkbox -->
            <div class="p-4 flex items-center border-b border-gray-200">
              <Checkbox 
                checked={lifeInsuranceSelected}
                on:change={toggleLifeInsurance}
              >
                <span class="font-medium">{lifeInsuranceOptions.label}</span>
              </Checkbox>
            </div>
            {#if lifeInsuranceSelected}
              <div class="border-t border-gray-100">
                <Accordion flush>
                  <AccordionItem>
                    <span slot="header">Life Insurance Options</span>
                    <div class="p-4 grid grid-cols-1 md:grid-cols-2 gap-2">
                      <Checkbox 
                        checked={selectedLifeInsuranceOptions.includes('wholeLife')}
                        on:change={() => toggleLifeInsuranceOption('wholeLife')}
                      >
                        Whole Life
                      </Checkbox>
                      <Checkbox 
                        checked={selectedLifeInsuranceOptions.includes('termLife')}
                        on:change={() => toggleLifeInsuranceOption('termLife')}
                      >
                        Term Life
                      </Checkbox>
                      <Checkbox 
                        checked={selectedLifeInsuranceOptions.includes('endowment')}
                        on:change={() => toggleLifeInsuranceOption('endowment')}
                      >
                        Endowment
                      </Checkbox>
                      <Checkbox 
                        checked={selectedLifeInsuranceOptions.includes('annuity')}
                        on:change={() => toggleLifeInsuranceOption('annuity')}
                      >
                        Annuity
                      </Checkbox>
                    </div>
                  </AccordionItem>
                </Accordion>
              </div>
            {/if}
          </div>
        </div>
      </AccordionItem>
    </Accordion>
  
    <!-- Saved Settings Table -->
    {#if savedSettings.length > 0}
      <div class="mt-6">
        <h3 class="text-xl font-semibold mb-2">Saved Insurance Product Configurations</h3>
        <div class="bg-white border border-gray-300 rounded-lg overflow-hidden">
          {#each savedSettings as setting, settingIndex}
            <div class="p-4 {settingIndex > 0 ? 'border-t border-gray-200' : ''}">
              <h4 class="font-semibold text-lg mb-3">Configuration #{settingIndex + 1}</h4>
              <div class="space-y-4">
                {#each setting.products as product}
                  <div class="ml-4">
                    <p class="font-medium">{product.productName}</p>
                    {#if product.subOptions.length > 0}
                      <div class="ml-6 mt-2 space-y-2">
                        {#each product.subOptions as subOption}
                          <div>
                            <p class="text-sm text-gray-700">{subOption.category}:</p>
                            <p class="ml-4 text-sm">{subOption.options.join(', ')}</p>
                          </div>
                        {/each}
                      </div>
                    {/if}
                  </div>
                {/each}
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}
  </div>
  
<div>
    <Accordion>
    <AccordionItem>
        <span slot="header">รหัสรถยนต์ประเภทต่างๆ</span>
        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
            <th scope="col" class="px-6 py-3">
                ประเภท
            </th>
            <th scope="col" class="px-6 py-3">
                รหัสรถยนต์
            </th>
            <th scope="col" class="px-6 py-3">
                ลักษณะการใช้รถยนต์
            </th>
            <th scope="col" class="px-6 py-3">
                คำอธิบาย
            </th>
            </tr>
        </thead>
        <tbody>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                รถยนต์นั่ง (ไม่เกิน 7 ที่นั่ง)
            </th>
            <td class="px-6 py-4">
                110
            </td>
            <td class="px-6 py-4">
                การใช้ส่วนบุคคล
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                120
            </td>
            <td class="px-6 py-4">
                การใช้เพื่อการพาณิชย์
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                รถยนต์โดยสาร (นั่งเกิน 7 ที่นั่ง)
            </th>
            <td class="px-6 py-4">
                210
            </td>
            <td class="px-6 py-4">
                การใช้ส่วนบุคคล
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                220
            </td>
            <td class="px-6 py-4">
                การใช้เพื่อการพาณิชย์
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                230
            </td>
            <td class="px-6 py-4">
                การใช้รับจ้างสาธารณะ
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                รถยนต์บรรทุก
            </th>
            <td class="px-6 py-4">
                320
            </td>
            <td class="px-6 py-4">
                การใช้เพื่อการพาณิชย์
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                340
            </td>
            <td class="px-6 py-4">
                การใช้เพื่อการพาณิชย์
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                รถยนต์ลากจูง
            </th>
            <td class="px-6 py-4">
                420
            </td>
            <td class="px-6 py-4">
                การใช้เพื่อการพาณิชย์
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                รถพ่วง
            </th>
            <td class="px-6 py-4">
                520
            </td>
            <td class="px-6 py-4">
                การใช้เพื่อการพาณิชย์
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                540
            </td>
            <td class="px-6 py-4">
                การใช้เพื่อการพาณิชย์
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                รถจักรยานยนต์
            </th>
            <td class="px-6 py-4">
                610
            </td>
            <td class="px-6 py-4">
                การใช้ส่วนบุคคล
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                620
            </td>
            <td class="px-6 py-4">
                การใช้เพื่อการพาณิชย์
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                630
            </td>
            <td class="px-6 py-4">
                การใช้รับจ้างสาธารณะ
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                รถยนต์นั่งรับจ้าง (ไม่เกิน 7 ที่นั่ง)
            </th>
            <td class="px-6 py-4">
                730
            </td>
            <td class="px-6 py-4">
                การใช้รับจ้างสาธารณะ
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                อื่นๆ
            </th>
            <td class="px-6 py-4">
                801
            </td>
            <td class="px-6 py-4">
                รถยนต์ป้ายแดง
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                802
            </td>
            <td class="px-6 py-4">
                รถพยาบาล
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                803
            </td>
            <td class="px-6 py-4">
                รถดับเพลิง
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                804
            </td>
            <td class="px-6 py-4">
                รถใช้ในการเกษตร
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                805
            </td>
            <td class="px-6 py-4">
                รถใช้ในการก่อสร้าง
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
            <tr class="bg-white dark:bg-gray-800">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                
            </th>
            <td class="px-6 py-4">
                806
            </td>
            <td class="px-6 py-4">
                อื่นๆ
            </td>
            <td class="px-6 py-4">
                
            </td>
            </tr>
        </tbody>
        </table>
    </AccordionItem>
    </Accordion>
</div>