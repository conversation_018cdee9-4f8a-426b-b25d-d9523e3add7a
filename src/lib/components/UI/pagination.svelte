<script lang="ts">
	import { P } from 'flowbite-svelte';

	export let currentPage: number = 1;
	export let totalPages: number = 10;
	export let visibleCount: number = 5;
	export let updateCurrentPage: (currentPage: number) => void;

	function getPaginationRange(
		currentPage: number,
		totalPages: number,
		visibleCount = 5
	): (number | string)[] {
		const pagination: (number | string)[] = [];

		if (totalPages <= visibleCount) {
			return Array.from({ length: totalPages }, (_, i) => i + 1);
		}

		const alwaysVisible = 2; // first and last
		const remainingSlots = visibleCount - alwaysVisible;
		const showLeftDots = currentPage > 3;
		const showRightDots = currentPage < totalPages - 2;

		let middlePages: (number | string)[] = [];

		if (!showLeftDots && showRightDots) {
			// Near the beginning
			for (let i = 2; i < 2 + remainingSlots; i++) {
				middlePages.push(i);
			}
			middlePages.push('...');
		} else if (showLeftDots && !showRightDots) {
			// Near the end
			middlePages.push('...');
			for (let i = totalPages - remainingSlots; i < totalPages; i++) {
				middlePages.push(i);
			}
		} else if (showLeftDots && showRightDots) {
			// In the middle
			const sideCount = Math.floor((remainingSlots - 1) / 2);
			const start = Math.max(2, currentPage - sideCount);
			const end = Math.min(totalPages - 1, start + remainingSlots - 1);

			if (start > 2) middlePages.push('...');
			for (let i = start; i <= end; i++) {
				middlePages.push(i);
			}
			if (end < totalPages - 1) middlePages.push('...');
		}

		return [1, ...middlePages, totalPages];
	}

	$: paginationItems = getPaginationRange(currentPage, totalPages, visibleCount);

	function prevPage() {
		if (currentPage > 1) {
			currentPage--;
			updateCurrentPage(currentPage);
		}
	}

	function nextPage() {
		if (currentPage < totalPages) {
			currentPage++;
			updateCurrentPage(currentPage);
		}
	}

	function goToPage(selectedPage: number) {
		if (selectedPage !== currentPage) {
			updateCurrentPage(selectedPage);
		}
	}

	$: pageInput = currentPage;

	function goToInputPage() {
		const pageNumber = pageInput;
		if (!isNaN(pageNumber) && pageNumber >= 1 && pageNumber <= totalPages) {
			updateCurrentPage(pageNumber);
		}
	}
</script>

<div class="pagination">
	<a
		href="#"
		class:selected={false}
		class:disabled={currentPage === 1}
		on:click|preventDefault={() => currentPage > 1 && prevPage()}
	>
		{'<'}
	</a>

	{#each paginationItems as item}
		{#if item === '...'}
			<span class="dots">...</span>
		{:else}
			<a
				href="#"
				class:selected={currentPage === item}
				on:click|preventDefault={() => goToPage(item)}
			>
				{item}
			</a>
		{/if}
	{/each}

	<a
		href="#"
		class:selected={false}
		class:disabled={currentPage === totalPages}
		on:click|preventDefault={() => currentPage < totalPages && nextPage()}
	>
		{'>'}
	</a>

	<div class="page-jump">
		<P>Page:</P>
		<input
			type="number"
			bind:value={pageInput}
			min="1"
			max={totalPages}
			placeholder=""
			on:keydown={(e) => e.key === 'Enter' && goToInputPage()}
		/>
		<span>/ {totalPages}</span>
	</div>
</div>

<style>
	.pagination {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 6px;
		margin: 25px auto;
		flex-wrap: wrap;
		font-family: sans-serif;
	}

	.pagination a,
	.pagination .dots {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 40px;
		height: 40px;
		font-size: 14px;
		text-align: center;
		text-decoration: none;
		color: #1A56DB;
		border: 1px solid #d1d5db;
		border-radius: 6px;
		cursor: pointer;
		background-color: white;
		transition: all 0.2s ease-in-out;
	}

	.pagination a:hover:not(.selected) {
		background-color: #f0f4f8;
	}

	.pagination a.selected {
		background-color: #1A56DB;
		color: white;
		font-weight: 600;
	}

	.pagination .dots {
		pointer-events: none;
		color: #999;
		border: none;
	}

	.pagination a.disabled {
		color: #bbb;
		border-color: #ddd;
		background-color: #f5f5f5;
		cursor: not-allowed;
		pointer-events: none; /* prevents all interaction */
	}

	.page-jump {
		display: flex;
		align-items: center;
		gap: 5px;
		margin-left: 30px;
	}

	.page-jump input {
		width: 50px;
		height: 36px;
		text-align: center;
		border: 1px solid #d1d5db;
		border-radius: 6px;
		font-size: 14px;
		padding: 4px;
	}

	.page-jump span {
		font-size: 14px;
		color: #555;
	}
</style>
