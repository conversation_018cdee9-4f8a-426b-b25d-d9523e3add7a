<script lang="ts">
	import type { Customer } from '$lib/types/customer';
	export let customer: Customer;
</script>

<div class="p-6">
	<div class="text-center text-gray-500 mt-8">
		<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
		</svg>
		<h3 class="mt-2 text-sm font-medium text-gray-900">No Timeline Events</h3>
		<p class="mt-1 text-sm text-gray-500">Customer activity timeline will appear here.</p>
	</div>
</div>