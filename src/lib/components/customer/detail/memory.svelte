<script lang="ts">
    import { displayDate } from '$lib/utils';
    import { TrashBinOutline } from 'flowbite-svelte-icons';
    import { enhance } from '$app/forms';
    import { t } from '$src/lib/stores/i18n';
    export let memories: any[];

    // reference for whichever memory-form is being clicked
    let deleteForm: HTMLFormElement;

    // optional: you can show a toast or something on success
    let showSuccess = false;
</script>

<div class="border rounded-lg overflow-hidden">
  {#if memories.length === 0}
    <div class="p-4 text-center text-gray-500">
      {t('no_memory')}
    </div>
  {:else}
    {#each [...memories].sort((a,b)=> new Date(b.created_on) - new Date(a.created_on)) as memory (memory.id)}
      <div class="grid grid-cols-[1fr_auto_auto] gap-4 p-4 border-b last:border-b-0 hover:bg-gray-50 items-start">

        <!-- description -->
        <div class="text-left text-gray-700">
          <div class="font-medium">{memory.detail_en}</div>
          <div class="text-sm text-gray-500">{memory.detail_th}</div>
        </div>

        <!-- timestamp -->
        <div class="text-sm text-gray-500 text-right w-32 self-start">
          <div>{displayDate(memory.created_on).date}</div>
          <div>{displayDate(memory.created_on).time}</div>
        </div>

        <!-- delete button -->
        <div class="text-right self-start">
          <form
            bind:this={deleteForm}
            action="?/delete_memory"
            method="POST"
            use:enhance={() => {
              return async ({ update, result }) => {
                if (result.success === true) {
                  await update();
                  showSuccess = true;
                  window.location.reload();
                }
              }
            }}
          >
            <!-- send the memory ID -->
            <input type="hidden" name="memoryId" value={memory.id} />

            <!-- clicking this will submit via requestSubmit -->
            <button
              type="button"
              on:click={() => deleteForm.requestSubmit()}
              class="text-gray-400 hover:text-red-600 p-1 rounded-full"
              aria-label="Delete memory"
            >
              <TrashBinOutline class="h-4 w-4" />
            </button>
          </form>
        </div>

      </div>
    {/each}
  {/if}
</div>

{#if showSuccess}
  <div class="fixed bottom-4 right-4 bg-green-100 text-green-800 px-4 py-2 rounded">
    Memory deleted!
  </div>
{/if}
