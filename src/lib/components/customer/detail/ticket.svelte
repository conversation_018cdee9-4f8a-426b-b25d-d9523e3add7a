<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { 
        EditSolid, 
        TicketSolid, 
    } from 'flowbite-svelte-icons';
    import {
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
        Tooltip,
    } from 'flowbite-svelte';
    
    import Pagination from '$src/lib/components/UI/pagination.svelte';

    import { 
        formatTimestamp,
        displayDate, 
        timeAgo, 
        getStatusClass, 
        getPriorityClass, 
        getSentimentClass, 
        getSentimentIcon 
    } from '$lib/utils';


    export let customer_tickets: any;

    $: sortedTickets = customer_tickets;

    //////////////// Pagination Logic ////////////////
    // pagination state variables
	let currentPage = 1;
	let itemsPerPage = 10;

    $: totalPages = Math.ceil(Math.max((sortedTickets.tickets ?? []).length, 1) / itemsPerPage);
    $: paginatedDocuments = (sortedTickets.tickets ?? []).slice(0, itemsPerPage);

    function updatePagination() {
		const idx = (currentPage - 1) * itemsPerPage;
		paginatedDocuments = sortedTickets.tickets.slice(idx, Math.min(idx + itemsPerPage, sortedTickets.tickets.length));
	}
    
    function updateCurrentPage(newCurrentPage: number) {
        currentPage = newCurrentPage;        
        updatePagination();
    }

    function getOverdueTickets(tickets) {
        const now = new Date();

        return tickets.filter(ticket => {
            const createdAt = new Date(ticket.created_at);
            const hoursDiff = (now - createdAt) / (1000 * 60 * 60);

            return hoursDiff > 8 && ticket.status.toLowerCase() !== 'close';
        }).length;
    }
</script>

<!-- Ticket details -->
<!-- <div class="mb-4 flex items-center justify-start">
    <TicketSolid class="text-black-500 h-5 w-5" />
    <h3 class="mb-0 ml-2 text-xl font-semibold">Ticket Information</h3>
</div> -->
<!--Summary Ticket-->
<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
    <!-- Total Tickets -->
    <div class="bg-gray-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-gray-100 mr-3">
            <TicketSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-gray-500 text-sm">{t('tickets')}</p>
            <p class="text-2xl font-bold">{customer_tickets.tickets.length}</p>
        </div>
    </div>

    <!-- Overdue Tickets -->
    <div class="bg-gray-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-gray-100 mr-3">
            <TicketSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-gray-500 text-sm">{t('overdue_ticket')}</p>
            <p class="text-2xl font-bold">{getOverdueTickets(customer_tickets.tickets)}</p>
        </div>
    </div>
</div>

{#if sortedTickets.length === 0}
    <p>{t('no_tickets_found')}</p>
{:else}
        <!-- <div class="overflow-x-auto"> -->
        <!-- <Table shadow class="table-fixed w-full"> -->
        <Table shadow > 
            <TableHead>
                <TableHeadCell>{t('table_no')}</TableHeadCell>
                <TableHeadCell>{t('table_status')}</TableHeadCell>
                <TableHeadCell>{t('table_priority')}</TableHeadCell>
                <TableHeadCell>{t('table_sentiment')}</TableHeadCell>
                <TableHeadCell>{t('table_agent')}</TableHeadCell>
                <TableHeadCell>{t('table_time')}</TableHeadCell>
                <TableHeadCell>{t('table_updated_on')}</TableHeadCell>
                            
            </TableHead>
            <TableBody>
                {#each paginatedDocuments as ticket}
                <TableBodyRow>
                    <TableBodyCell>
                        <a
                            href="/monitoring/{ticket.id}"
                            class="flex items-center justify-start text-blue-600 hover:underline py-2"
                        >
                            {ticket.id}<EditSolid class="h-4 w-4" />
                        </a>
                    </TableBodyCell>
                    <TableBodyCell>
                        <div class="flex justify-start">
                            <span class={`${getStatusClass(ticket.status_id)} px-3 py-1 rounded-md text-sm w-32 text-center`}>
                                {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                            </span>
                        </div>
                    </TableBodyCell>
                    
                    <TableBodyCell>
                        <div class="flex justify-start">  
                            <span class={`${getPriorityClass(ticket.priority.name)} p-2 rounded-md text-sm w-24`}>
                                {ticket.priority.name ?? "-"}
                            </span>
                        </div>                        
                    </TableBodyCell>
                    <TableBodyCell>
                        <div class="flex justify-center"> 
                            <div class={`flex items-center justify-center gap-1 rounded-md p-2 ${getSentimentClass(ticket.latest_analysis?.sentiment)}`}>
                                <img
                                    src={getSentimentIcon(ticket.latest_analysis?.sentiment)}
                                    alt={ticket.latest_analysis?.sentiment}
                                    class="w-5 h-5"
                                />
                                <Tooltip>{ticket.latest_analysis?.sentiment ?? 'Unclassified'}</Tooltip>
                            </div>
                        </div>
                    </TableBodyCell>
                    <TableBodyCell>
                        <div class="text-sm">{ticket.owner.name ? ticket.owner.name : '-'}</div>
                        <div class="text-xs text-gray-500">
                            {ticket.owner?.roles?.length
                                ? ticket.owner.roles.map(role => role.name).join(', ')
                                : '-'}
                        </div>            
                    </TableBodyCell>

                    <TableBodyCell>
                        <span class="text-sm">{timeAgo(ticket.updated_on, ticket.status)}</span>
                    </TableBodyCell>

                    <TableBodyCell>
                        <div class="text-sm">{displayDate(ticket.updated_on).date}</div>
                        <div class="text-xs text-gray-500">{displayDate(ticket.updated_on).time}</div>
                    </TableBodyCell>
                </TableBodyRow>
                {/each}
            </TableBody>
        </Table>

        <Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />
{/if}

<style>
    /* Custom Scrollbar */
    .custom-scrollbar::-webkit-scrollbar {
        width: 8px; /* Thinner scrollbar */
    }
    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f1f1; /* Light track color */
        border-radius: 10px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #888; /* Thumb color */
        border-radius: 10px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #555; /* Thumb color on hover */
    }
</style>