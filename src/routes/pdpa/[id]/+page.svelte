<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import liff from '@line/liff';
	import { getBackendUrl } from '$lib/config';
	import { Radio, Checkbox } from "flowbite-svelte";

	// Get LIFF ID from URL parameter
	$: liffId = $page.params.id;

	// Form data
	let formData = {
		firstName: '',
		lastName: '',
		nationalId: '',
		birthDay: '',
		birthMonth: '',
		birthYear: '',
		phoneNumber: '',
		customerType: '' // ลูกค้า, ตัวแทน, นายหน้า
	};

	// LIFF profile data
	let liffProfile = {
		userId: '',
		displayName: '',
		pictureUrl: ''
	};

	let isLiffInitialized = false;
	let liffError = '';

	// Form validation
	let errors = {};
	let isSubmitting = false;
	let submitMessage = '';
	let consentAccepted = false;

	// Generate options for date dropdowns
	let days = Array.from({ length: 31 }, (_, i) => i + 1);
	let months = [
		{ value: '01', label: 'มกราคม' },
		{ value: '02', label: 'กุมภาพันธ์' },
		{ value: '03', label: 'มีนาคม' },
		{ value: '04', label: 'เมษายน' },
		{ value: '05', label: 'พฤษภาคม' },
		{ value: '06', label: 'มิถุนายน' },
		{ value: '07', label: 'กรกฎาคม' },
		{ value: '08', label: 'สิงหาคม' },
		{ value: '09', label: 'กันยายน' },
		{ value: '10', label: 'ตุลาคม' },
		{ value: '11', label: 'พฤศจิกายน' },
		{ value: '12', label: 'ธันวาคม' }
	];

	// Customer type options
	let customerTypes = [
		{ value: 'customer', label: 'ลูกค้า' },
		{ value: 'agent', label: 'ตัวแทน' },
		{ value: 'broker', label: 'นายหน้า' }
	];

	// Buddhist years (current year + 543 - 100 years to current year + 543)
	let currentYear = new Date().getFullYear();
	let buddhistCurrentYear = currentYear + 543;
	let years = Array.from({ length: 100 }, (_, i) => buddhistCurrentYear - i);

	// Initialize LIFF with dynamic ID
	async function initializeLiff() {
		try {
			// Validate LIFF ID format
			if (!liffId || !isValidLiffId(liffId)) {
				throw new Error('Invalid LIFF ID format');
			}

			await liff.init({ liffId: liffId });
			isLiffInitialized = true;

			// Check if user is logged in
			if (liff.isLoggedIn()) {
				await getUserProfile();
			} else {
				// Redirect to LINE login
				liff.login();
			}
		} catch (error) {
			console.error('LIFF initialization failed:', error);
			liffError = 'ไม่สามารถเชื่อมต่อกับ LINE ได้ หรือ LIFF ID ไม่ถูกต้อง';
		}
	}

	// Validate LIFF ID format
	function isValidLiffId(id) {
		// LIFF ID format: xxxxxxxxxx-xxxxxxxx (10 digits, dash, 8 characters)
		return /^\d{10}-[a-zA-Z0-9]{8}$/.test(id);
	}

	// Get user profile from LIFF
	async function getUserProfile() {
		try {
			const profile = await liff.getProfile();
			liffProfile = {
				userId: profile.userId,
				displayName: profile.displayName,
				pictureUrl: profile.pictureUrl || ''
			};
			console.log('LIFF Profile:', liffProfile);
		} catch (error) {
			console.error('Failed to get profile:', error);
			liffError = 'ไม่สามารถดึงข้อมูลโปรไฟล์ได้';
		}
	}

	// Component mount - initialize when liffId changes
	$: if (liffId && !isLiffInitialized) {
		initializeLiff();
	}

	// Reset state when LIFF ID changes
	$: if (liffId) {
		// Reset LIFF state when ID changes
		isLiffInitialized = false;
		liffError = '';
		liffProfile = {
			userId: '',
			displayName: '',
			pictureUrl: ''
		};
	}

	// Validate National ID (Thai format)
	function validateNationalId(id) {
		const cleanId = id.replace(/[-\s]/g, '');
		if (cleanId.length !== 13) return false;
		
		let sum = 0;
		for (let i = 0; i < 12; i++) {
			sum += parseInt(cleanId[i]) * (13 - i);
		}
		const checksum = (11 - (sum % 11)) % 10;
		return checksum === parseInt(cleanId[12]);
	}

	// Validate phone number (Thai format)
	function validatePhoneNumber(phone) {
		const cleanPhone = phone.replace(/[-\s]/g, '');
		return /^(06|08|09)\d{8}$/.test(cleanPhone) || /^02\d{7}$/.test(cleanPhone);
	}

	// Form validation function
	function validateForm() {
		errors = {};

		if (!formData.firstName.trim()) {
			errors.firstName = 'กรุณากรอกชื่อ';
		}

		if (!formData.lastName.trim()) {
			errors.lastName = 'กรุณากรอกนามสกุล';
		}

		if (!formData.nationalId.trim()) {
			errors.nationalId = 'กรุณากรอกเลขบัตรประชาชน';
		} else if (formData.nationalId.replace(/\D/g, '').length !== 13) {
			errors.nationalId = 'กรุณากรอกเลข 13 หลัก';
		} else if (!validateNationalId(formData.nationalId)) {
			errors.nationalId = 'เลขบัตรประชาชนไม่ถูกต้อง';
		}

		if (!formData.birthDay || !formData.birthMonth || !formData.birthYear) {
			errors.birthDate = 'กรุณาเลือกวันเดือนปีเกิด';
		}

		if (!formData.phoneNumber.trim()) {
			errors.phoneNumber = 'กรุณากรอกเบอร์ติดต่อ';
		} else if (formData.phoneNumber.replace(/\D/g, '').length !== 10) {
			errors.phoneNumber = 'กรุณากรอกเบอร์ 10 หลัก';
		} else if (!validatePhoneNumber(formData.phoneNumber)) {
			errors.phoneNumber = 'เบอร์ติดต่อไม่ถูกต้อง';
		}

		if (!formData.customerType) {
			errors.customerType = 'กรุณาเลือกประเภทลูกค้า';
		}

		return Object.keys(errors).length === 0;
	}

	// Check if form is complete
	$: isFormComplete = 
		formData.firstName.trim() &&
		formData.lastName.trim() &&
		formData.nationalId.trim() &&
		formData.birthDay &&
		formData.birthMonth &&
		formData.birthYear &&
		formData.phoneNumber.trim() &&
		formData.customerType &&
		consentAccepted &&
		validateForm() &&
		isLiffInitialized &&
		liffProfile.userId;

	// Computed value for the actual data structure that will be sent (including LIFF data)
	$: transformedData = {
		// Form data
		first_name: formData.firstName,
		last_name: formData.lastName,
		national_id: formData.nationalId,
		date_of_birth: formData.birthYear && formData.birthMonth && formData.birthDay 
			? `${formData.birthYear - 543}-${formData.birthMonth}-${formData.birthDay.toString().padStart(2, '0')}`
			: '',
		phone: formData.phoneNumber,
		customer_type: formData.customerType,
		// LIFF profile data
		line_user_id: liffProfile.userId,
		line_display_name: liffProfile.displayName,
		line_picture_url: liffProfile.pictureUrl,
		liff_id: liffId
	};

	// Handle form submission
	async function handleSubmit() {
		if (!validateForm() || !consentAccepted || !isLiffInitialized || !liffProfile.userId) {
			console.error('Form validation failed or LIFF not ready');
			return;
		}

		isSubmitting = true;
		submitMessage = '';

		try {
			const backendUrl = getBackendUrl();
			const response = await fetch(`${backendUrl}/api/pdpa-consent`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(transformedData)
			});

			if (response.ok) {
				submitMessage = 'บันทึกข้อมูลเรียบร้อยแล้ว';
				// Reset form
				formData = {
					firstName: '',
					lastName: '',
					nationalId: '',
					birthDay: '',
					birthMonth: '',
					birthYear: '',
					phoneNumber: '',
					customerType: ''
				};
				consentAccepted = false;

				// Show success message for 2 seconds then reload
				setTimeout(() => {
					window.location.reload();
				}, 2000);
			} else {
				submitMessage = 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง';
			}
		} catch (error) {
			console.error('Submit error:', error);
			submitMessage = 'เกิดข้อผิดพลาดในการเชื่อมต่อ กรุณาลองใหม่อีกครั้ง';
		} finally {
			isSubmitting = false;
		}
	}

	// Format National ID input
	function formatNationalId(event) {
		let value = event.target.value.replace(/\D/g, '');
		if (value.length > 13) value = value.slice(0, 13);
		formData.nationalId = value;
	}

	// Format phone number input
	function formatPhoneNumber(event) {
		let value = event.target.value.replace(/\D/g, '');
		if (value.length > 10) value = value.slice(0, 10);
		formData.phoneNumber = value;
	}
</script>

<svelte:head>
	<title>ข้อมูลส่วนบุคคล - {liffId}</title>
	<meta name="description" content="แบบฟอร์มยินยอมการเก็บรวบรวม ใช้ และเปิดเผยข้อมูลส่วนบุคคล" />
</svelte:head>

<div class="container">
	<!-- Invalid LIFF ID State -->
	{#if liffId && !isValidLiffId(liffId)}
		<div class="error-section">
			<p class="error-message">LIFF ID ไม่ถูกต้อง: {liffId}</p>
			<p class="error-message">กรุณาตรวจสอบ URL ของคุณ</p>
		</div>
	{:else if !liffId}
		<div class="error-section">
			<p class="error-message">ไม่พบ LIFF ID</p>
			<p class="error-message">กรุณาเข้าถึงผ่าน LINE Official Account</p>
		</div>
	{:else}
		<!-- LIFF Loading/Error State -->
		{#if !isLiffInitialized}
			<div class="loading-section">
				<p>กำลังเชื่อมต่อกับ LINE...</p>
				<p class="liff-id-display">LIFF ID: {liffId}</p>
			</div>
		{:else if liffError}
			<div class="error-section">
				<p class="error-message">{liffError}</p>
				<p class="liff-id-display">LIFF ID: {liffId}</p>
			</div>
		{:else}
			<div class="header">
				<h1>ข้อมูลส่วนบุคคล</h1>
				<p class="subtitle">ข้อมูลสุขภาพเป็นข้อมูลส่วนบุคคล ตาม พ.ร.บ. คุ้มครองข้อมูลส่วนบุคคล (PDPA)กรุณายืนยันตัวตน และยินยอมให้แจ้งข้อมูลส่วนบุคคลและข้อมูล ด้านสุขภาพ เป็นการประมวลผลเพื่อการดำเนินการให้บริการต่อครั้งเท่านั้น</p>
			</div>

			<!-- Rest of your form HTML remains the same... -->
			<form on:submit|preventDefault={handleSubmit} class="form">
				<!-- Name Row -->
				<div class="form-row">
					<div class="form-group">
						<label for="firstName" class:error={errors.firstName}>ชื่อจริง <span class="required">*</span></label>
						<input
							type="text"
							id="firstName"
							bind:value={formData.firstName}
							class:error={errors.firstName}
							placeholder="กรุณากรอกชื่อจริง"
						/>
						{#if errors.firstName}
							<span class="error-message">{errors.firstName}</span>
						{/if}
					</div>

					<div class="form-group">
						<label for="lastName" class:error={errors.lastName}>นามสกุล <span class="required">*</span></label>
						<input
							type="text"
							id="lastName"
							bind:value={formData.lastName}
							class:error={errors.lastName}
							placeholder="กรุณากรอกนามสกุล"
						/>
						{#if errors.lastName}
							<span class="error-message">{errors.lastName}</span>
						{/if}
					</div>
				</div>

				<!-- National ID -->
				<div class="form-group">
					<label for="nationalId" class:error={errors.nationalId}>เลขบัตรประชาชน <span class="required">*</span></label>
					<input
						type="text"
						id="nationalId"
						value={formData.nationalId}
						on:input={formatNationalId}
						class:error={errors.nationalId}
						placeholder="กรุณากรอกเลขบัตรประชาชน 13 หลัก"
						maxlength="13"
					/>
					{#if errors.nationalId}
						<span class="error-message">{errors.nationalId}</span>
					{/if}
				</div>

				<!-- Birth Date -->
				<div class="form-group">
					<label class:error={errors.birthDate}>วัน/เดือน/ปีเกิด <span class="required">*</span></label>
					<div class="date-row">
						<select bind:value={formData.birthDay} class:error={errors.birthDate}>
							<option value="">วัน</option>
							{#each days as day}
								<option value={day}>{day}</option>
							{/each}
						</select>

						<select bind:value={formData.birthMonth} class:error={errors.birthDate}>
							<option value="">เดือน</option>
							{#each months as month}
								<option value={month.value}>{month.label}</option>
							{/each}
						</select>

						<select bind:value={formData.birthYear} class:error={errors.birthDate}>
							<option value="">ปี พ.ศ.</option>
							{#each years as year}
								<option value={year}>{year}</option>
							{/each}
						</select>
					</div>
					{#if errors.birthDate}
						<span class="error-message">{errors.birthDate}</span>
					{/if}
				</div>

				<!-- Phone Number -->
				<div class="form-group">
					<label for="phoneNumber" class:error={errors.phoneNumber}>เบอร์ติดต่อ <span class="required">*</span></label>
					<input
						type="tel"
						id="phoneNumber"
						value={formData.phoneNumber}
						on:input={formatPhoneNumber}
						class:error={errors.phoneNumber}
						placeholder="กรุณากรอกเบอร์ติดต่อ"
						maxlength="10"
					/>
					{#if errors.phoneNumber}
						<span class="error-message">{errors.phoneNumber}</span>
					{/if}
				</div>

				<!-- Customer Type -->
				<div class="form-group">
					<label class:error={errors.customerType}>ประเภทลูกค้า <span class="required">*</span></label>
					<div class="radio-row">
						<Radio
							bind:group={formData.customerType}
							color="green"
							value="customer"
							class="radio-input"
						>
							ลูกค้า
						</Radio>
						<Radio
							bind:group={formData.customerType}
							color="green"
							value="agent"
							class="radio-input"
						>
							ตัวแทน
						</Radio>
						<Radio
							bind:group={formData.customerType}
							color="green"
							value="broker"
							class="radio-input"
						>
							นายหน้า
						</Radio>
					</div>
					{#if errors.customerType}
						<span class="error-message">{errors.customerType}</span>
					{/if}
				</div>

				<!-- Consent Section -->
				<div class="consent-section">
					<h3>การให้ความยินยอม</h3>
					<div class="consent-checkbox">
						<Checkbox
							bind:checked={consentAccepted}
							color="green"
							class="consent-checkbox-input"
						>
							ข้าพเจ้ายินยอมให้องค์กรเก็บรวบรวม ใช้ และเปิดเผยข้อมูลส่วนบุคคลของข้าพเจ้า เพื่อวัตถุประสงค์
							ในการให้บริการและติดต่อสื่อสาร ตามพ.ร.บ. คุ้มครองข้อมูลส่วนบุคคล พ.ศ. 2562
						</Checkbox>
					</div>
				</div>


				<!-- Submit Button -->
				<button type="submit" class="submit-btn" disabled={isSubmitting || !isFormComplete}>
					{isSubmitting ? 'กำลังส่งข้อมูล...' : 'ส่งข้อมูล'}
				</button>

				{#if submitMessage}
					<div class="submit-message" class:success={submitMessage.includes('เรียบร้อย')}>
						{submitMessage}
					</div>
				{/if}
			</form>

			<!-- Debug Section -->
			<div class="debug-section">
				<h3>Debug: API Data (Transformed)</h3>
				<pre>{JSON.stringify(transformedData, null, 2)}</pre>
				<p><strong>Current LIFF ID:</strong> {liffId}</p>
				<p><strong>Consent Accepted:</strong> {consentAccepted}</p>
				<p><strong>Form Complete:</strong> {isFormComplete}</p>
				<p><strong>LIFF Initialized:</strong> {isLiffInitialized}</p>
				<p><strong>Errors:</strong> {JSON.stringify(errors, null, 2)}</p>
			</div>
		{/if}
	{/if}
</div>

<style>
	* {
		box-sizing: border-box;
	}

	.container {
		max-width: 600px;
		margin: 0 auto;
		padding: 2rem;
		font-family: 'Inter', 'Sarabun', 'Kanit', -apple-system, BlinkMacSystemFont, sans-serif;
		background: #ffffff;
		min-height: 100vh;
	}

	.loading-section,
	.error-section {
		text-align: center;
		padding: 2rem;
		margin-bottom: 2rem;
	}

	.loading-section p {
		color: #666666;
		font-size: 1.1rem;
	}

	.error-section .error-message {
		color: #dc2626;
		font-size: 1.1rem;
		font-weight: 500;
	}

	.profile-section {
		background: linear-gradient(135deg, #00C300 0%, #00B900 100%);
		padding: 1.5rem;
		border-radius: 12px;
		margin-bottom: 2rem;
		color: white;
	}

	.profile-info {
		display: flex;
		align-items: center;
		gap: 1rem;
	}

	.profile-image {
		width: 60px;
		height: 60px;
		border-radius: 50%;
		object-fit: cover;
		border: 3px solid rgba(255, 255, 255, 0.3);
	}

	.profile-name {
		font-size: 1.2rem;
		font-weight: 600;
		margin: 0 0 0.25rem 0;
	}

	.profile-id {
		font-size: 0.9rem;
		opacity: 0.8;
		margin: 0;
	}

	.header {
		text-align: center;
		margin-bottom: 3rem;
	}

	.header h1 {
		font-size: 2rem;
		font-weight: 600;
		color: #1a1a1a;
		margin: 0 0 0.5rem 0;
	}

	.subtitle {
		color: #666666;
		font-size: 1rem;
		margin: 0;
		font-weight: 400;
	}

	.form {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.form-row {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 1rem;
	}

	.form-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.form-group label {
		font-weight: 500;
		color: #1a1a1a;
		font-size: 0.95rem;
	}

	.form-group label.error {
		color: #dc2626;
	}

	.required {
		color: #dc2626;
		font-weight: 600;
	}

	.form-group input,
	.form-group select {
		padding: 0.875rem 1rem;
		border: 1.5px solid #e5e5e5;
		border-radius: 8px;
		font-size: 1rem;
		background: white;
		transition: all 0.2s ease;
		font-family: inherit;
	}

	.form-group input:focus,
	.form-group select:focus {
		outline: none;
		border-color: #3b82f6;
		box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
	}

	.form-group input.error,
	.form-group select.error {
		border-color: #dc2626;
	}

	.form-group input::placeholder {
		color: #9ca3af;
	}

	.radio-row {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr;
		gap: 1rem;
		margin-top: 0.5rem;
	}

	.radio-row :global(.radio-input) {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.75rem;
		background: white;
		border: 1.5px solid #e5e5e5;
		border-radius: 8px;
		cursor: pointer;
		transition: all 0.2s ease;
		font-size: 0.95rem;
		font-weight: 500;
	}

	.radio-row :global(.radio-input:hover) {
		border-color: #3b82f6;
		background: #f8faff;
	}

	.radio-row :global(.radio-input input[type="radio"]) {
		width: 1.125rem;
		height: 1.125rem;
		margin: 0;
		accent-color: #3b82f6;
	}

	/* Responsive - Keep 3 columns but adjust spacing and sizing */
	@media (max-width: 768px) {
		.radio-row {
			gap: 0.75rem;
		}
		
		.radio-row :global(.radio-input) {
			padding: 0.625rem 0.5rem;
			font-size: 0.9rem;
		}
	}

	/* For very small screens, reduce padding and font size but maintain 3 columns */
	@media (max-width: 480px) {
		.radio-row {
			gap: 0.5rem;
		}
		
		.radio-row :global(.radio-input) {
			padding: 0.5rem 0.25rem;
			font-size: 0.85rem;
		}
		
		.radio-row :global(.radio-input input[type="radio"]) {
			width: 1rem;
			height: 1rem;
		}
	}

	.helper-text {
		font-size: 0.875rem;
		color: #666666;
		margin-top: -0.25rem;
	}

	.error-message {
		font-size: 0.875rem;
		color: #dc2626;
		margin-top: -0.25rem;
	}

	.date-row {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr;
		gap: 0.75rem;
	}

	.date-row select {
		appearance: none;
		background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
		background-position: right 0.75rem center;
		background-repeat: no-repeat;
		background-size: 1.25rem;
		padding-right: 2.75rem;
	}

	.consent-section {
		margin-top: 1rem;
		padding: 1.5rem;
		background: #f9fafb;
		border-radius: 12px;
		border: 1px solid #e5e7eb;
	}

	.consent-section h3 {
		margin: 0 0 1rem 0;
		font-size: 1.1rem;
		font-weight: 600;
		color: #1a1a1a;
	}

	.consent-checkbox {
		display: flex;
		align-items: flex-start;
		gap: 0.75rem;
	}

	/* Custom styling for Flowbite Checkbox */
	.consent-checkbox :global(.consent-checkbox-input) {
		align-items: flex-start;
		gap: 0.75rem;
	}

	.consent-checkbox :global(.consent-checkbox-input label) {
		color: #374151;
		line-height: 1.5;
		cursor: pointer;
		font-size: 0.95rem;
		margin-left: 0.5rem;
	}

	.consent-checkbox :global(.consent-checkbox-input input[type="checkbox"]) {
		width: 1.25rem;
		height: 1.25rem;
		margin-top: 0.125rem;
		flex-shrink: 0;
	}

	.submit-btn {
		background: #1a1a1a;
		color: white;
		border: none;
		padding: 1rem 2rem;
		font-size: 1rem;
		font-weight: 500;
		border-radius: 8px;
		cursor: pointer;
		transition: all 0.2s ease;
		margin-top: 1rem;
		font-family: inherit;
	}

	.submit-btn:hover:not(:disabled) {
		background: #333333;
		transform: translateY(-1px);
	}

	.submit-btn:disabled {
		background: #9ca3af;
		cursor: not-allowed;
		transform: none;
	}

	.submit-message {
		padding: 1rem;
		border-radius: 8px;
		text-align: center;
		font-weight: 500;
		margin-top: 1rem;
	}

	.submit-message.success {
		background: #ecfdf5;
		color: #059669;
		border: 1px solid #a7f3d0;
	}

	.submit-message:not(.success) {
		background: #fef2f2;
		color: #dc2626;
		border: 1px solid #fecaca;
	}

	.debug-section {
		margin-top: 2rem;
		padding: 1rem;
		background: #f8f9fa;
		border-radius: 8px;
		border: 1px solid #e9ecef;
	}

	.debug-section h3 {
		margin: 0 0 1rem 0;
		font-size: 1rem;
		color: #495057;
	}

	.debug-section pre {
		background: #ffffff;
		padding: 1rem;
		border-radius: 4px;
		border: 1px solid #dee2e6;
		overflow-x: auto;
		font-size: 0.875rem;
		margin: 0 0 1rem 0;
	}

	.debug-section p {
		margin: 0.5rem 0;
		font-size: 0.875rem;
		color: #6c757d;
	}

	/* Responsive - Updated to maintain same layout on smaller screens */
	@media (max-width: 768px) {
		.container {
			padding: 1.5rem 1rem;
		}

		.header h1 {
			font-size: 1.75rem;
		}

		.profile-info {
			gap: 0.75rem;
		}

		.profile-image {
			width: 50px;
			height: 50px;
		}

		.profile-name {
			font-size: 1.1rem;
		}

		/* Keep the same grid layout but with smaller gaps and padding */
		.form-row {
			gap: 0.5rem; /* Reduced gap for smaller screens */
		}

		.date-row {
			gap: 0.5rem; /* Reduced gap for smaller screens */
		}

		/* Reduce font size and padding for smaller screens */
		.form-group input,
		.form-group select {
			padding: 0.75rem 0.875rem;
			font-size: 0.95rem;
		}

		.form-group label {
			font-size: 0.9rem;
		}
	}

	/* Extra small screens - maintain layout but with even smaller spacing */
	@media (max-width: 480px) {
		.container {
			padding: 1rem 0.5rem;
		}

		.form-row {
			gap: 0.25rem;
		}

		.date-row {
			gap: 0.25rem;
		}

		.form-group input,
		.form-group select {
			padding: 0.625rem 0.75rem;
			font-size: 0.9rem;
		}

		.form-group label {
			font-size: 0.85rem;
		}

		.date-row select {
			padding-right: 2.5rem;
		}

		.profile-info {
			flex-direction: column;
			text-align: center;
			gap: 0.5rem;
		}
	}
</style>