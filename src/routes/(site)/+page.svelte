<script lang="ts">
    import { EditSolid } from 'flowbite-svelte-icons';
    import { onMount } from 'svelte';
    import { AccordionItem, Accordion } from 'flowbite-svelte';
    import type { PageData } from './$types';

    import { goto } from '$app/navigation';

    import Ticketpreview from '$src/lib/components/homepage/ticketpreview.svelte';
    import Userpreview from '$src/lib/components/homepage/userpreview.svelte';
    import Midpreview from '$src/lib/components/homepage/midpreview.svelte';

    export let data: PageData;
    $: ({ tickets, users, current_user } = data);
    
    let pageTitle = "Home";
    let searchQuery: string = "";

    function getInitials(name: string): string {
        if (!name) return "";
        return name.split(' ')
            .map(part => part.charAt(0).toUpperCase())
            .join('')
            .substring(0, 2);
    }

    const lastActive = new Date();

    onMount(() => {
        goto('/chat_center');
    });
</script>
