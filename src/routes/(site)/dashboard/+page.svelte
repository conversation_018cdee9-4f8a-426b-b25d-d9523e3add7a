<script lang="ts">
    import { onMount } from 'svelte';
    import { fetchDashboard, type DashboardData } from '$lib/api/features/dashboard/dashboard';
    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
    import { t } from '$src/lib/stores/i18n';
    
    let currentDashboard: DashboardData | null = null;
    let error: string = '';
    let activeMainTab = 'team-performance'; // Main tab: 'work-quality' or 'team-performance'
    let activeSubTabId = 9; // Sub-tab dashboard ID
    let isLoading = false;
    let windowWidth: number;
    
    // Main tabs
    const mainTabs = [
        { id: 'team-performance', name: 'team_performance' },
        { id: 'work-quality', name: 'work_quality' }
    ];
    
    // Sub-tabs for each main category
    const teamPerformanceTabs = [
        // { id: 3, name: 'human_agent_performance_all' },
        // { id: 4, name: 'human_agent_performance_single' },
        // { id: 2, name: 'unresolved_tickets_dashboard' },
        // { id: 5, name: 'agent_online_offline_statistics' },
        { id: 9, name: 'agent_performance' },
        { id: 10, name: 'chat_performance' },
        { id: 11, name: 'response_time_volume'}
    ];

    const workQualityTabs = [
        // { id: 1, name: 'customer_sentiment' },
        // { id: 6, name: 'customer_topic_breakdown' },
        // { id: 7, name: 'usefulness_of_ai' },
        // { id: 8, name: 'usefulness_of_ai_2' },
        { id: 12, name: 'work_quality' }
    ];

    // Get current sub-tabs based on active main tab
    $: currentSubTabs = activeMainTab === 'work-quality' ? workQualityTabs : teamPerformanceTabs;

    async function loadDashboard(id: number) {
        isLoading = true;
        error = '';
        
        try {
            const dashboard = await fetchDashboard(id);
            currentDashboard = dashboard;
        } catch (e) {
            error = `${t('failed_to_load_dashboard')} ${id}`;
            currentDashboard = null;
        } finally {
            isLoading = false;
        }
    }

    function switchMainTab(tabId: string) {
        activeMainTab = tabId;
        
        // Set default sub-tab for each main tab
        if (tabId === 'work-quality') {
            activeSubTabId = 12; // Default to Customer Sentiment
        } else if (tabId === 'team-performance') {
            activeSubTabId = 9; // Default to Human Agent Performance Dashboard (All)
        }
        
        loadDashboard(activeSubTabId);
    }

    async function switchSubTab(tabId: number) {
        if (tabId === activeSubTabId) return;
        activeSubTabId = tabId;
        await loadDashboard(activeSubTabId);
    }

    onMount(async () => {
        await loadDashboard(activeSubTabId);
    });
</script>

<svelte:window bind:innerWidth={windowWidth} />

<svelte:head>
    <title>{t('analytics_dashboard')}</title>
</svelte:head>

<div class="flex h-screen bg-gray-50">
	<div class="w-full h-full bg-white flex flex-col">
        <!-- Header -->
        <div class="bg-white border-b border-gray-200 px-6 py-4">
            <Breadcrumb aria-label="Default breadcrumb example" class="mb-4">
                <BreadcrumbItem href="/" home>
                    <span class="text-gray-400">{t('home')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem>
                    <span class="text-gray-700">{t('analytics_dashboard')}</span>
                </BreadcrumbItem>
            </Breadcrumb>
            
            <h1 class="text-3xl font-bold text-gray-900">{t('analytics_dashboard')}</h1>
        </div>

        <!-- Main Tab Navigation -->
        <div class="bg-white border-b border-gray-200">
            <nav class="px-6">
                <div class="flex space-x-8">
                    {#each mainTabs as tab}
                        <button
                            class="py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200 {
                                activeMainTab === tab.id 
                                    ? 'border-blue-500 text-blue-600' 
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }"
                            on:click={() => switchMainTab(tab.id)}
                        >
                            <span class="flex items-center space-x-2">
                                <span>{t(tab.name)}</span>
                            </span>
                        </button>
                    {/each}
                </div>
            </nav>
        </div>

        <!-- Sub-Tab Navigation -->
        <div class="bg-gray-50 border-b border-gray-200">
            <nav class="px-6">
                <div class="flex flex-wrap gap-2 py-4">
                    {#each currentSubTabs as tab}
                        <button
                            class="px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 {
                                activeSubTabId === tab.id 
                                    ? 'bg-blue-100 text-blue-700 border border-blue-300' 
                                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
                            }"
                            on:click={() => switchSubTab(tab.id)}
                        >
                            {t(tab.name)}
                        </button>
                    {/each}
                </div>
            </nav>
        </div>

        <!-- Dashboard Content -->
        <div class="flex-1 h-full">
            {#if error}
                <div class="m-6 bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                        <p class="text-red-700 font-medium">{error}</p>
                    </div>
                </div>
            {:else if isLoading}
                <div class="h-full flex justify-center items-center">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
                        <p class="text-gray-500">{t('loading_dashboard')}</p>
                    </div>
                </div>
            {:else if currentDashboard?.dashboard_url}
                <div class="h-full">
                    <div class="bg-white h-full overflow-hidden">
                        <div style="height: 100%; overflow: hidden; position: relative;">
                            <iframe
                                title={currentDashboard.title}
                                src={currentDashboard.dashboard_url}
                                style="width: 100%; height: calc(100% + 80px); position: absolute; top: -80px; left: 0; border: none;"
                                loading="lazy"
                                sandbox="allow-scripts allow-same-origin allow-forms"
                            ></iframe>
                        </div>
                    </div>
                </div>
            {:else}
                <div class="h-full flex items-center justify-center bg-gray-50">
                    <div class="text-center">
                        <h2 class="text-xl font-semibold text-gray-900 mb-2">{t('no_dashboard_available')}</h2>
                        <p class="text-gray-500">{t('select_dashboard_message')}</p>
                    </div>
                </div>
            {/if}
        </div>
    </div>
</div>