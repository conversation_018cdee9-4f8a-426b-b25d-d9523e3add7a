<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import { t } from '$lib/stores/i18n';
    import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'flowbite-svelte';
    import { TicketSolid, UserHeadsetOutline } from 'flowbite-svelte-icons';

    // Config: Number of tickets to load per request
    const TICKETS_PER_LOAD = 1;

    export let tickets: any[] = [];
    export let selectedTicketId: number | null = null; // Currently clicked/selected ticket
    export let customerName: string = '';
    export let hasMore: boolean = false;
    export let loading: boolean = false;

    const dispatch = createEventDispatcher();

    function handleTicketClick(ticket: any) {
        dispatch('select', {
            ticketId: ticket.id
        });
    }

    function handleLoadMore() {
        if (!loading && hasMore) {
            dispatch('loadMore', {
                limit: TICKETS_PER_LOAD
            });
        }
    }

    // Need to check. This one is from other project.
    function formatTime(dateString: string): string {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now.getTime() - date.getTime();

        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
        if (diff < 604800000) return `${Math.floor(diff / 86400000)}d ago`; // 7 days

        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    function getStatusColor(status: string): string {
        switch (status?.toLowerCase()) {
            case 'open':
                return 'green';
            case 'assigned':
                return 'blue';
            case 'closed':
                return 'gray';
            case 'pending':
                return 'yellow';
            default:
                return 'gray';
        }
    }

    function getPriorityColor(priority: string): string {
        switch (priority?.toLowerCase()) {
            case 'high':
                return 'red';
            case 'medium':
                return 'yellow';
            case 'low':
                return 'gray';
            default:
                return 'gray';
        }
    }
</script>

<div class="flex h-full flex-col">
    <!-- Header -->
    <div class="border-b border-gray-200 p-4">
        <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold">{t('tickets')}</h2>
            <span class="text-sm text-gray-500">{tickets.length} {t('tickets')}</span>
        </div>
        <div class="mt-2">
            <p class="text-sm text-gray-600">{customerName}</p>
        </div>
    </div>

    <!-- Ticket List -->
    <div class="flex-1 overflow-y-auto bg-gray-50">
        {#if tickets.length === 0}
            <div class="p-8 text-center text-gray-500">
                <p>{t('no_tickets_found')}</p>
            </div>
        {:else}
            <div class="divide-y divide-gray-100">
                {#each tickets as ticket (ticket.id)}
                    <button
                        class="relative w-full p-4 text-left transition-colors
                               {selectedTicketId === ticket.id
                                ? 'bg-blue-100 pl-6 hover:bg-blue-100'
                                : 'hover:bg-gray-100'}"
                        on:click={() => handleTicketClick(ticket)}
                    >
                        {#if selectedTicketId === ticket.id}
                            <!-- Blue border for selected ticket -->
                            <div
                                class="absolute left-0 top-0 flex h-full w-1 items-center justify-center bg-blue-500"
                            ></div>
                        {/if}
                        
                        <div class="flex items-start justify-between">
                            <div class="min-w-0 flex-1 pr-2">
                                <!-- Ticket ID and Status -->
                                <div class="mb-1 flex items-center gap-2">
                                    <span
                                        class="font-medium {selectedTicketId === ticket.id
                                            ? 'text-blue-800'
                                            : 'text-gray-900'}"
                                    >
                                        #{ticket.id}
                                    </span>
                                    <Badge color={getStatusColor(ticket.status?.name || ticket.status)}>
                                        {ticket.status?.name || ticket.status || 'Unknown'}
                                    </Badge>
                                </div>

                                <!-- Ticket Subject/Title -->
                                {#if ticket.subject}
                                    <div
                                        class="mt-1 truncate text-sm {selectedTicketId === ticket.id
                                            ? 'text-blue-800'
                                            : 'text-gray-600'}"
                                    >
                                        {ticket.subject}
                                    </div>
                                {/if}

                                <!-- Owner Information -->
                                {#if ticket.owner}
                                    <div class="mt-2 flex items-center gap-1">
                                        <UserHeadsetOutline class="h-3 w-3 text-gray-400" />
                                        <span class="text-xs text-gray-500">
                                            {ticket.owner.username || ticket.owner.full_name || 'Unknown'}
                                        </span>
                                    </div>
                                {/if}
                            </div>

                            <!-- Right Side Info -->
                            <div class="ml-2 flex flex-col items-end">
                                <!-- Time -->
                                <span
                                    class="whitespace-nowrap text-xs {selectedTicketId === ticket.id
                                        ? 'text-blue-800'
                                        : 'text-gray-600'}"
                                >
                                    {formatTime(ticket.updated_on || ticket.created_on)}
                                </span>

                                <!-- Priority -->
                                {#if ticket.priority}
                                    <div class="mt-1">
                                        <Badge size="xs" color={getPriorityColor(ticket.priority?.name || ticket.priority)}>
                                            {ticket.priority?.name || ticket.priority}
                                        </Badge>
                                    </div>
                                {/if}
                            </div>
                        </div>
                    </button>
                {/each}
            </div>
            
            <!-- Load More Button -->
            {#if hasMore}
                <div class="p-4">
                    <Button
                        color="light"
                        class="w-full"
                        disabled={loading}
                        on:click={handleLoadMore}
                    >
                        {#if loading}
                            <Spinner class="mr-2 h-4 w-4" />
                            {t('loading')}...
                        {:else}
                            {t('load_more')} {t('tickets')}
                        {/if}
                    </Button>
                </div>
            {/if}
        {/if}
    </div>
</div>

<style>
    .custom-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: #e5e7eb #f9fafb;
    }

    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f9fafb;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background-color: #e5e7eb;
        border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background-color: #d1d5db;
    }
</style> 