// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/private';
// import { env as publicEnv } from '$env/dynamic/public'; 
import { getBackendUrl } from '$src/lib/config';

import { services } from "$lib/api/features";
import { redirect, error, fail } from '@sveltejs/kit';
import type { PageServerLoad } from "../$types";
import type { Actions } from "@sveltejs/kit";

export const load: PageServerLoad = async ({ cookies }) => {
    let access_token = cookies.get('access_token')
    let refresh_token = cookies.get('refresh_token');

    if (!access_token) {
        return {
            system_setting: [],
            partners: [],
            departments : [],
            user_tags: [],
            customer_tags : [],
            schedules : [],
            error: 'No access token available'
        };
    }

    const response_userInfo = await services.users.getUserInfo(access_token);
    if (response_userInfo.res_status === 401) {
        throw error(401, 'Invalid access token!!!');
    }

    // Checking if the user is supervisor or admin in order to see the settings page
    const role = response_userInfo.users.roles[0].name;

    if (role !== 'Admin') {
        throw redirect(302, '/');
    }

    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            const response_system_setting = await services.system_setting.getAll(access_token);
            const response_partner = await services.companies.getAll(access_token);
            const response_department = await services.users.getAllDepartment(access_token);
            const response_user_tags = await services.users.getAllTags(access_token);
            const response_customer_tags = await services.customers.getFilterTags(access_token);
            // const response_schedule = await services.schedules.getBusinessHours(access_token);
            
            if (response_system_setting.res_status === 401 || response_partner.res_status === 401 || response_department.res_status === 401 || response_user_tags.res_status === 401 || response_customer_tags.res_status === 401) {
                throw error(401, 'Invalid access token!!!');
            }

            // console.log(`SETTING PAGE LOAD`)
            // console.log(response_system_setting.system_setting)
            // console.log(response_partner.partners)
            // console.log(response_user_tags.tags)
            // console.log(response_customer_tags.customer_tags)

            return {
                system_setting: response_system_setting.system_setting || [],
                partners: response_partner.partners || [],
                departments : response_department.departments || [],
                user_tags: response_user_tags.tags || [],
                customer_tags : response_customer_tags.data || [],
                // schedules: response_schedule.schedules || [],
            }

        } catch (err) {
            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');

            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }
};

export const actions: Actions = {
    update_system_setting: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        // Parse the settings from form data
        const settingsJson = formData.get('settings');
        const settings = settingsJson ? JSON.parse(settingsJson.toString()) : [];

        const url = `${getBackendUrl()}/setting/api/settings/`;

        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ "settings": settings })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Status: ${errorData.message || 'Unknown error'} (${response.status})`);
            }

            return { success: true };
        } catch (error) {
            console.error('Update Settings error:', error);
            return fail(500, { error: `${error.message}` });
        }
    },
    // ==================== Partner Actions ====================
    create_new_partner_action: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        // Get partner data from formData
        const name = formData.get('name')?.toString() ?? '';
        const code = formData.get('code')?.toString() ?? '';
        const color = formData.get('color');

        // Basic validation
        if (!name || !code) {
            return fail(400, { error: 'Name and code are required fields.' });
        }

        const partnerData = { name, code, color };

        try {
            const response = await services.companies.createCompany(partnerData, access_token);

            // if (response.res_status !== 200) {
            //     throw new Error(`Failed to create partner. Status: ${response.res_status}`);
            // }

            return { success: true, message: 'Partner created successfully' };

        } catch (error) {
            console.error('Error creating partner:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while creating the partner' });
        }
    },
    delete_partner: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        // Get company ID from formData
        const backend_company_id = formData.get('backend_company_id')?.toString();

        // Basic validation
        if (!backend_company_id) {
            return fail(400, { error: 'Company ID is required.' });
        }

        try {
            const response = await services.companies.deleteCompany(backend_company_id, access_token);

            return { success: true, message: 'Partner deleted successfully' };

        } catch (error) {
            console.error('Error deleting partner:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while deleting the partner' });
        }
    },
    update_partner: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        // Get company ID from formData
        const backend_company_id = formData.get('backend_company_id')?.toString();

        // Basic validation
        if (!backend_company_id) {
            return fail(400, { error: 'Company ID is required.' });
        }

        // Construct updatedData object (add additional fields as needed)
        const updatedData = {
            name: formData.get('name')?.toString(),
            code: formData.get('code')?.toString(),
            color: formData.get('color')
            // For example, add other fields like email, address, etc.
            // email: formData.get('email')?.toString(),
            // address: formData.get('address')?.toString(),
        };

        try {
            const response = await services.companies.updatedCompany(backend_company_id, access_token, updatedData);

            return { success: true, message: 'Partner updated successfully' };
        } catch (error) {
            console.error('Error updating partner:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while updating the partner' });
        }
    },
    // ==================== Department Actions ====================
    create_new_department_action: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');
        
        // Get department data from formData
        const name = formData.get('name')?.toString() ?? '';
        const code = formData.get('code')?.toString() ?? '';
        const description = formData.get('description')?.toString() ?? '';
        const is_active = formData.get('is_active')?.toString() === 'true';
        const color = formData.get('color');
        
        // Basic validation
        if (!name || !code || !description) {
            return fail(400, { error: 'Name, code, and description are required fields.' });
        }
    
        const departmentData = { name, code, description, is_active, color};
    
        try {
            const response = await services.users.createDepartment(departmentData, access_token);
            return { success: true, message: 'Department created successfully' };
        } catch (error) {
            console.error('Error creating department:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while creating the department' });
        }
    },
    delete_department: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');
        
        // Get department ID from formData
        const backend_department_id = formData.get('backend_department_id')?.toString();
    
        if (!backend_department_id) {
            return fail(400, { error: 'Department ID is required.' });
        }
    
        try {
            const response = await services.users.deleteDepartment(backend_department_id, access_token);
            return { success: true, message: 'Department deleted successfully' };
        } catch (error) {
            console.error('Error deleting department:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while deleting the department' });
        }
    },
    update_department: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');
    
        // Get department ID from formData
        const backend_department_id = formData.get('backend_department_id')?.toString();
    
        if (!backend_department_id) {
            return fail(400, { error: 'Department ID is required.' });
        }
    
        // Construct updatedData object
        const updatedData = {
            name: formData.get('name')?.toString(),
            code: formData.get('code')?.toString(),
            description: formData.get('description')?.toString(),
            is_active: formData.get('is_active')?.toString() === 'true',
            color: formData.get('color')
        };
    
        try {
            const response = await services.users.updateDepartment(backend_department_id, updatedData, access_token);
            return { success: true, message: 'Department updated successfully' };
        } catch (error) {
            console.error('Error updating department:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while updating the department' });
        }
    },
    // ==================== Upload Actions ====================
    upload_image: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');
        
        // Get the image file and key from formData
        const imageFile = formData.get('image_file');
        const key = formData.get('key')?.toString();
        
        // Basic validation
        if (!imageFile || !(imageFile instanceof File)) {
            return fail(400, { error: 'Image file is required.' });
        }
        
        if (!key) {
            return fail(400, { error: 'Key is required.' });
        }
        
        try {
            const response = await services.system_setting.uploadImage(key, imageFile, access_token);

            return { 
                success: true, 
                message: 'Upload Image successfully',
            };
            
        } catch (error) {
            console.error('Image upload error:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while uploading the image' });
        }
    },
    // ==================== User Tags Actions ====================
    create_new_tag_action: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        // Get tag data from formData
        const name = formData.get('name')?.toString() ?? '';
        const color = formData.get('color')
        
        // console.log(formData)

        // Basic validation
        if (!name) {
            return fail(400, { error: 'Tag name is a required field.' });
        }

        const tagData = { name, color };

        try {
            const response = await services.users.createTag(tagData, access_token);
            return { success: true, message: 'Tag created successfully' };
        } catch (error) {
            console.error('Error creating tag:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while creating the tag' });
        }
    },
    delete_tag: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        // Get tag ID from formData
        const tag_id = formData.get('tag_id')?.toString();

        // Basic validation
        if (!tag_id) {
            return fail(400, { error: 'Tag ID is required.' });
        }

        try {
            const response = await services.users.deleteTag(tag_id, access_token);
            return { success: true, message: 'Tag deleted successfully' };
        } catch (error) {
            console.error('Error deleting tag:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while deleting the tag' });
        }
    },
    update_tag: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        // Get tag ID from formData
        const tag_id = formData.get('tag_id')?.toString();

        // Basic validation
        if (!tag_id) {
            return fail(400, { error: 'Tag ID is required.' });
        }

        // Construct updatedData object
        const updatedData = {
            name: formData.get('name')?.toString(),
            color: formData.get('color')
        };

        try {
            const response = await services.users.updateTag(tag_id, updatedData, access_token);
            return { success: true, message: 'Tag updated successfully' };
        } catch (error) {
            console.error('Error updating tag:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while updating the tag' });
        }
    },

    // ==================== Customer Tags Actions ====================
    create_new_customer_tag_action: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        console.log(formData)
        // Get tag data from formData
        const name = formData.get('name')?.toString() ?? '';

        // Basic validation
        if (!name) {
            return fail(400, { error: 'Customer tag name is a required field.' });
        }

        const tagData = { 
            name: name, 
            color: formData.get('color') 
        };

        try {
            const response = await services.customers.createCustomerTag(tagData, access_token);
            return { success: true, message: 'Customer tag created successfully' };
        } catch (error) {
            console.error('Error creating customer tag:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while creating the customer tag' });
        }
    },

    delete_customer_tag: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        // Get tag ID from formData
        const tag_id = formData.get('tag_id')?.toString();

        // Basic validation
        if (!tag_id) {
            return fail(400, { error: 'Customer tag ID is required.' });
        }

        try {
            const response = await services.customers.deleteCustomerTag(tag_id, access_token);
            return { success: true, message: 'Customer tag deleted successfully' };
        } catch (error) {
            console.error('Error deleting customer tag:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while deleting the customer tag' });
        }
    },

    update_customer_tag: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        // Get tag ID from formData
        const tag_id = formData.get('tag_id')?.toString();

        // Basic validation
        if (!tag_id) {
            return fail(400, { error: 'Customer tag ID is required.' });
        }

        // Construct updatedData object
        const updatedData = {
            name: formData.get('name')?.toString(),
            color: formData.get('color') 
        };

        try {
            const response = await services.customers.updateCustomerTag(tag_id, updatedData, access_token);
            return { success: true, message: 'Customer tag updated successfully' };
        } catch (error) {
            console.error('Error updating customer tag:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while updating the customer tag' });
        }
    },

    update_user_work_schedule: async ({ request, cookies }) => {
        const access_token = cookies.get('access_token');
        if (!access_token) {
            return fail(401, { error: 'Authentication required' });
        }
        
        try {
            const formData = await request.formData();
            const sameAsBusinessHours = formData.get('sameAsBusinessHours');
            const workShiftData = formData.get('workShiftData');
            // Prepare data for API
            const scheduleData = {
                sameAsBusinessHours: sameAsBusinessHours,
                workShift: JSON.parse(workShiftData)
            };
            console.log(scheduleData)
            
            const url = `${getBackendUrl()}/setting/api/schedule/business-hours/`;

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${access_token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(scheduleData)
                });
    
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`Status: ${errorData.message || 'Unknown error'} (${response.status})`);
                }
    
                return { 
                    success: true,
                    message: response.res_msg || 'Work schedule updated successfully' 
                };
            } catch (error) {
                console.error('Update Settings error:', error);
                return fail(500, { error: `${error.message}` });
            }
            
        } catch (err) {
            console.error('Error updating work schedule:', err);
            return fail(500, { error: 'Failed to update work schedule' });
        }
    }
}