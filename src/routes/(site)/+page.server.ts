// import { PUBLIC_BACKEND_URL } from "$env/static/public";
import { services } from "$lib/api/features";
import { redirect, error, fail } from '@sveltejs/kit';
import type { PageServerLoad } from "./$types";
import type { Actions } from "@sveltejs/kit";


export const load: PageServerLoad = async ({ params, cookies }) => {
    let access_token = cookies.get('access_token');
    let refresh_token = cookies.get('refresh_token');

    if (!access_token) {
        return {
            tickets: [],
            users: [],
            current_user: [],
            error: 'No access token available'
        };
    }

    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            const response_tickets = await services.tickets.getAll(access_token);
            const response_user = await services.users.getAll(access_token);
            const response_userInfo = await services.users.getUserInfo(access_token);

            if (response_tickets.res_status === 401 || response_user.res_status === 401 || response_userInfo.res_status === 401) {
                throw error(401, 'Invalid access token!!!');
            }
            // console.log(response_userInfo.users)
            // console.log(response_tickets.tickets)
            // const lineUsers = response_tickets.tickets.map(ticket => ticket.customer?.line_user);
            // console.log(lineUsers);
            

            return {
                tickets: response_tickets.tickets || [],
                users: response_user.users || [],
                current_user : response_userInfo.users || [],
            }
        } catch (err) {
            // console.error('Error fetching user details:', err);
            // error(500, 'Failed to load user details');

            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            // console.log("Refresh Response: ", refreshResponse);
            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }
};