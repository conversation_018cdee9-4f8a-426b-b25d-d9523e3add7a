<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import type { PageData } from './$types';
	import { customerStore, filteredCustomers } from '$lib/stores/customerStore';
	import CustomerListItem from '$lib/components/customers/CustomerListItem.svelte';
	import LoadingSpinner from '$lib/components/common/LoadingSpinner.svelte';
	
	export let data: PageData;
	
	let searchTerm = '';
	let loading = false;
	
	onMount(() => {
		// If we have customers from server, set them in store
		if (data.customers) {
			customerStore.setCustomers(data.customers);
			
			// If only one customer, redirect to their detail page
			if (data.customers.length === 1) {
				goto(`/chat_center/${data.customers[0].customer_id}`);
			}
		}
	});
	
	function handleCustomerClick(customerId: number) {
		goto(`/chat_center/${customerId}`);
	}
	
	function handleSearch(event: Event) {
		const target = event.target as HTMLInputElement;
		customerStore.setFilter('search', target.value);
	}
</script>

<div class="min-h-screen bg-gray-50">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
		<div class="bg-white shadow rounded-lg">
			<!-- Header -->
			<div class="px-6 py-4 border-b border-gray-200">
				<div class="flex items-center justify-between">
					<h1 class="text-2xl font-semibold text-gray-900">Customers</h1>
					<div class="flex items-center space-x-4">
						<!-- Search -->
						<div class="relative">
							<input
								type="text"
								placeholder="Search customers..."
								class="w-64 px-4 py-2 pl-10 pr-4 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
								on:input={handleSearch}
							/>
							<svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
							</svg>
						</div>
						
						<!-- Filter button -->
						<button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
							<svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
							</svg>
							Filter
						</button>
					</div>
				</div>
			</div>
			
			<!-- Customer List -->
			<div class="divide-y divide-gray-200">
				{#if loading}
					<div class="px-6 py-12 text-center">
						<LoadingSpinner />
					</div>
				{:else if $filteredCustomers.length === 0}
					<div class="px-6 py-12 text-center text-gray-500">
						{searchTerm ? 'No customers found matching your search.' : 'No customers found.'}
					</div>
				{:else}
					{#each $filteredCustomers as customer (customer.customer_id)}
						<CustomerListItem 
							{customer} 
							on:click={() => handleCustomerClick(customer.customer_id)}
						/>
					{/each}
				{/if}
			</div>
		</div>
	</div>
</div>