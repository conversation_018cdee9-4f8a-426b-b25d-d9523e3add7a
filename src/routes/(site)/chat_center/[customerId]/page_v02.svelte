<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { page } from '$app/stores';
	import CustomerPlatformList from '$lib/components/customers/CustomerPlatformList.svelte';
	import ConversationView from '$lib/components/conversation/ConversationView.svelte';
	import CustomerInfoPanel from '$lib/components/customer/CustomerInfoPanel.svelte';
	import { customerDetailStore } from '$lib/stores/customerDetailStore';
	import { customerWebSocket } from '$lib/websocket/customerWebSocket';
	import type { Customer, CustomerPlatformIdentity } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';
	
	let customerId: number;
	let customer: Customer | null = null;
	let selectedPlatformId: number | null = null;
	let loading = true;
	
	$: customerId = parseInt($page.params.customerId);
	
	onMount(async () => {
		await loadCustomerData();
		// Connect WebSocket
		customerWebSocket.connect(customerId);
		customerWebSocket.subscribeToCustomer(customerId);
	});
	
	onDestroy(() => {
		customerWebSocket.disconnect();
	});
	
	async function loadCustomerData() {
		try {
			loading = true;
			// const response = await fetch(`/api/customers/${customerId}/`);
			// const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/`,
			// 	{
			// 		method: 'GET',
			// 		credentials: 'include'
			// 	}
			// );
			const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/`, {
				credentials: 'include' // This ensures cookies are sent
			});
			if (response.ok) {
				const data = await response.json();
				customer = data;
				customerDetailStore.setCustomer(data);
			}
		} catch (error) {
			console.error('Error loading customer:', error);
		} finally {
			loading = false;
		}
	}
	
	function handlePlatformSelect(platformId: number) {
		selectedPlatformId = platformId;
		customerDetailStore.selectPlatform(platformId);
	}
</script>

<div class="flex h-screen bg-gray-100">
	<!-- Left Panel: Platform Identities List -->
	<div class="w-80 bg-white border-r border-gray-200 flex flex-col">
		<CustomerPlatformList 
			{customerId}
			{selectedPlatformId}
			on:select={(e) => handlePlatformSelect(e.detail)}
		/>
	</div>
	
	<!-- Middle Panel: Conversation -->
	<div class="flex-1 flex flex-col bg-white">
		{#if selectedPlatformId}
			<ConversationView 
				{customerId}
				platformId={selectedPlatformId}
			/>
		{:else}
			<div class="flex-1 flex items-center justify-center text-gray-500">
				Select a platform to view conversation
			</div>
		{/if}
	</div>
	
	<!-- Right Panel: Customer Info -->
	<div class="w-96 bg-white border-l border-gray-200">
		{#if customer}
			<CustomerInfoPanel {customer} />
		{/if}
	</div>
</div>

<style>
	/* Custom scrollbar styles */
	:global(.custom-scrollbar) {
		scrollbar-width: thin;
		scrollbar-color: #e5e7eb #f9fafb;
	}
	
	:global(.custom-scrollbar::-webkit-scrollbar) {
		width: 6px;
	}
	
	:global(.custom-scrollbar::-webkit-scrollbar-track) {
		background: #f9fafb;
	}
	
:global(.custom-scrollbar::-webkit-scrollbar-thumb) {
		background-color: #e5e7eb;
		border-radius: 3px;
	}
	
	:global(.custom-scrollbar::-webkit-scrollbar-thumb:hover) {
		background-color: #d1d5db;
	}
</style>