<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { page } from '$app/stores';
    import { goto } from '$app/navigation';
    import ConversationView from '$lib/components/conversation/ConversationView.svelte';
    import CustomerInfoPanel from '$lib/components/customer/CustomerInfoPanel.svelte';
    import { customerDetailStore } from '$lib/stores/customerDetailStore';
    import { customerWebSocket } from '$lib/websocket/customerWebSocket';
    import type { Customer, CustomerPlatformIdentity } from '$lib/types/customer';
    import { getBackendUrl } from '$src/lib/config';
    import type { PageData } from './$types';
    
    export let data: PageData;
    
    let customerId: number;
    let customer: Customer | null = data.customer;
    let selectedPlatformId: number | null = data.selectedPlatformId;
    let loading = false;
    let searchTerm = '';
    
    $: customerId = parseInt($page.params.customerId);
    
    onMount(async () => {
        // Set initial data
        if (data.customer) {
            customerDetailStore.setCustomer(data.customer);
        }
        if (data.selectedPlatformId) {
            customerDetailStore.selectPlatform(data.selectedPlatformId);
        }
        
        // Connect WebSocket
        customerWebSocket.connect(customerId);
        customerWebSocket.subscribeToCustomer(customerId);
    });
    
    onDestroy(() => {
        customerWebSocket.disconnect();
    });
    
    function handlePlatformSelect(platformIdentity: any) {
        // Update URL with new platform selection
        goto(`/chat_center/${platformIdentity.customer}?platform=${platformIdentity.id}`, { 
            replaceState: true,
            keepFocus: true,
            noScroll: true 
        });
        
        selectedPlatformId = platformIdentity.id;
        customerDetailStore.selectPlatform(platformIdentity.id);
        
        // Load customer data if different
        if (platformIdentity.customer !== customerId) {
            loadCustomerData(platformIdentity.customer);
        }
    }
    
    async function loadCustomerData(newCustomerId: number) {
        try {
            loading = true;
            const response = await fetch(`${getBackendUrl()}/customer/api/customers/${newCustomerId}/`, {
                credentials: 'include'
            });
            if (response.ok) {
                const data = await response.json();
                customer = data;
                customerDetailStore.setCustomer(data);
                customerId = newCustomerId;
                
                // Reconnect WebSocket for new customer
                customerWebSocket.disconnect();
                customerWebSocket.connect(newCustomerId);
                customerWebSocket.subscribeToCustomer(newCustomerId);
            }
        } catch (error) {
            console.error('Error loading customer:', error);
        } finally {
            loading = false;
        }
    }
    
    function handleSearch(event: Event) {
        const target = event.target as HTMLInputElement;
        searchTerm = target.value;
    }
    
    // Helper function to get platform icon
    function getPlatformIcon(platform: string) {
        const icons = {
            'facebook': '📘',
            'instagram': '📷',
            'twitter': '🐦',
            'whatsapp': '💬',
            'telegram': '✈️',
            'line': '💚',
            'wechat': '💬'
        };
        return icons[platform.toLowerCase()] || '💬';
    }
    
    // Filter platform identities based on search
    $: filteredPlatformIdentities = data.allPlatformIdentities.filter(p => {
        if (!searchTerm) return true;
        const search = searchTerm.toLowerCase();
        return (
            p.platform_username?.toLowerCase().includes(search) ||
            p.platform_user_id?.toLowerCase().includes(search) ||
            p.platform?.toLowerCase().includes(search)
        );
    });
</script>

<div class="flex h-screen bg-gray-100">
    <!-- Left Panel: All Platform Identities List -->
    <div class="w-80 bg-white border-r border-gray-200 flex flex-col">
        <!-- Header -->
        <div class="px-4 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Conversations</h2>
            <!-- Search -->
            <div class="mt-3 relative">
                <input
                    type="text"
                    placeholder="Search conversations..."
                    class="w-full px-3 py-2 pl-9 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    on:input={handleSearch}
                    bind:value={searchTerm}
                />
                <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </div>
        </div>
        
        <!-- Platform Identities List -->
        <div class="flex-1 overflow-y-auto custom-scrollbar">
            {#each filteredPlatformIdentities as platformIdentity (platformIdentity.id)}
                <button
                    class="w-full px-4 py-3 hover:bg-gray-50 transition-colors duration-200 border-b border-gray-100 text-left
                           {selectedPlatformId === platformIdentity.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''}"
                    on:click={() => handlePlatformSelect(platformIdentity)}
                >
                    <div class="flex items-center space-x-3">
                        <!-- Platform Icon -->
                        <div class="flex-shrink-0 w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <span class="text-xl">{getPlatformIcon(platformIdentity.platform)}</span>
                        </div>
                        
                        <!-- Platform Info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <p class="font-medium text-gray-900 truncate">
                                    {platformIdentity.platform_username || platformIdentity.platform_user_id}
                                </p>
                                {#if platformIdentity.last_message_time}
                                    <span class="text-xs text-gray-500">
                                        {new Date(platformIdentity.last_message_time).toLocaleTimeString('en-US', { 
                                            hour: '2-digit', 
                                            minute: '2-digit' 
                                        })}
                                    </span>
                                {/if}
                            </div>
                            <div class="flex items-center mt-1">
                                <span class="text-xs text-gray-500 capitalize">{platformIdentity.platform}</span>
                                {#if platformIdentity.unread_count > 0}
                                    <span class="ml-auto bg-blue-500 text-white text-xs rounded-full px-2 py-0.5">
                                        {platformIdentity.unread_count}
                                    </span>
                                {/if}
                            </div>
                            {#if platformIdentity.last_message}
                                <p class="text-sm text-gray-600 truncate mt-1">
                                    {platformIdentity.last_message}
                                </p>
                            {/if}
                        </div>
                    </div>
                </button>
            {/each}
        </div>
    </div>
    
    <!-- Middle Panel: Conversation -->
    <div class="flex-1 flex flex-col bg-white">
        {#if selectedPlatformId}
            <ConversationView 
                {customerId}
                platformId={selectedPlatformId}
            />
        {:else}
            <div class="flex-1 flex items-center justify-center text-gray-500">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No conversation selected</h3>
                    <p class="mt-1 text-sm text-gray-500">Choose a conversation from the list to start messaging</p>
                </div>
            </div>
        {/if}
    </div>
    
    <!-- Right Panel: Customer Info -->
    <div class="w-96 bg-white border-l border-gray-200">
        {#if customer && selectedPlatformId}
            <CustomerInfoPanel {customer} />
        {:else}
            <div class="flex items-center justify-center h-full text-gray-500">
                <p>Select a conversation to view customer details</p>
            </div>
        {/if}
    </div>
</div>

<style>
    /* Custom scrollbar styles */
    :global(.custom-scrollbar) {
        scrollbar-width: thin;
        scrollbar-color: #e5e7eb #f9fafb;
    }
    
    :global(.custom-scrollbar::-webkit-scrollbar) {
        width: 6px;
    }
    
    :global(.custom-scrollbar::-webkit-scrollbar-track) {
        background: #f9fafb;
    }
    
    :global(.custom-scrollbar::-webkit-scrollbar-thumb) {
        background-color: #e5e7eb;
        border-radius: 3px;
    }
    
    :global(.custom-scrollbar::-webkit-scrollbar-thumb:hover) {
        background-color: #d1d5db;
    }
</style>