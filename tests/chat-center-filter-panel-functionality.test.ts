/**
 * End-to-End Test: Chat Center Filter Panel Functionality Workflow
 *
 * This comprehensive test validates the complete filter functionality in the chat center page,
 * ensuring that all filter options (unread, agent/owner, channel/platform) work correctly
 * and properly filter chat items based on their attributes with round-trip testing patterns.
 *
 * SVELTEKIT PAGES TESTED:
 * - /chat_center (+page.svelte) - Main chat center interface with platform identity list
 *   └── Loads platform identities via +page.server.ts load function
 *   └── Integrates PlatformIdentityList and FilterPanel components
 *
 * SVELTE COMPONENTS TESTED:
 * - PlatformIdentityList.svelte (/src/lib/components/chat/PlatformIdentityList.svelte)
 *   └── Filter button: #platform-list-filter-button (line 716)
 *   └── Chat items: #platform-list-chat-item-{identity.id} (line 764)
 *   └── Customer names: #platform-list-chat-item-customer-name-{identity.id} (line 827)
 *   └── Owner badges: #platform-list-chat-item-owner-badge-{identity.id} (line 922)
 *   └── Channel badges: #platform-list-chat-item-channel-badge-{identity.id} (line 915)
 *   └── Unread counts: #platform-list-chat-item-unread-count-{identity.id} (line 895)
 *
 * - FilterPanel.svelte (/src/lib/components/chat/FilterPanel.svelte)
 *   └── Container: #filter-panel-container (line 371)
 *   └── Unread filters: #filter-panel-unread-{filter.id} (line 405)
 *   └── Owner filters: #filter-panel-owner-{option.id} (line 482)
 *   └── Channel filters: #filter-panel-channel-{option.id} (line 543)
 *   └── Clear button: #filter-panel-clear-button (line 573)
 *   └── Apply button: #filter-panel-apply-button (line 581)
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to chat center page (/chat_center)
 * 2. PlatformIdentityList component loading verification
 * 3. Data collection from all status tabs (My Assigned, Open, Other Assigned)
 * 4. Filter panel opening and interaction testing
 * 5. Unread filter functionality verification
 * 6. Agent/Owner filter functionality verification
 * 7. Channel/Platform filter functionality verification
 * 8. Filter reset and combination testing
 * 9. Round-trip testing to ensure test environment consistency
 *
 * LANGUAGE-AGNOSTIC TESTING:
 * Uses DOM attributes (disabled states, visibility, data attributes), element counts,
 * and structural assertions instead of text-based detection to ensure tests work
 * across different language configurations and remain robust against UI text changes.
 *
 * ROUND-TRIP TESTING:
 * Implements comprehensive filter state verification by capturing original data,
 * applying filters, verifying results, then resetting filters to restore original
 * state, preventing test environment pollution and ensuring test isolation.
 */

import { test, expect } from '@playwright/test';
import { performLoginWithRedirectHandling } from './utils/auth.utils';

// Interface for collected chat data
interface ChatData {
	identityId: string;
	customerName: string;
	ownerName: string;
	channelName: string;
	platformName: string;
	hasUnread: boolean;
	ticketId: string;
}

// Interface for filter test data
interface FilterTestData {
	myAssigned: ChatData[];
	open: ChatData[];
	otherAssigned: ChatData[];
	allChats: ChatData[];
	uniqueOwners: string[];
	uniqueChannels: string[];
	uniquePlatforms: string[];
}

/**
 * Wait for PlatformIdentityList component to load completely
 */
async function waitForPlatformIdentityListLoad(page) {
	console.log('Waiting for PlatformIdentityList component to load...');

	// Wait for the main container
	await expect(page.locator('#platform-list-chat-center-header')).toBeVisible();

	// Wait for tabs to be present
	await expect(page.locator('#platform-list-chat-tabs')).toBeVisible();

	// Wait for either chat items or empty state
	await page.waitForFunction(
		() => {
			const chatItems = document.querySelector('#platform-list-chat-items-list');
			const emptyState = document.querySelector('#platform-list-empty-state');
			return (chatItems && chatItems.children.length > 0) || emptyState;
		},
		{ timeout: 10000 }
	);

	console.log('✓ PlatformIdentityList component loaded successfully');
}

/**
 * Switch to a specific tab and wait for content to load
 */
async function switchToTab(page, tabId: string): Promise<boolean> {
	console.log(`Switching to ${tabId} tab...`);

	const tabButton = page.locator(`#platform-list-chat-tab-${tabId}`);
	await expect(tabButton).toBeVisible();
	await tabButton.click();

	// Wait for tab to become active
	await expect(tabButton).toHaveAttribute('aria-selected', 'true');

	// Wait for content to load
	await page.waitForTimeout(1000);

	// Check if tab has content
	const emptyState = page.locator('#platform-list-empty-state');
	const chatItems = page.locator('#platform-list-chat-items-list button[data-identity-id]');

	const isEmpty = await emptyState.isVisible();
	const hasItems = (await chatItems.count()) > 0;

	if (isEmpty) {
		console.log(`  → Tab ${tabId} is empty (no chats)`);
		return false;
	} else if (hasItems) {
		const itemCount = await chatItems.count();
		console.log(`  → Tab ${tabId} has ${itemCount} chat(s)`);
		return true;
	}

	return false;
}

/**
 * Extract chat data from a specific tab
 */
async function extractChatDataFromTab(page, tabId: string): Promise<ChatData[]> {
	console.log(`Extracting chat data from ${tabId} tab...`);

	const hasContent = await switchToTab(page, tabId);
	if (!hasContent) {
		console.log(`  → No data to extract from ${tabId} tab`);
		return [];
	}

	const chatItems = page.locator('#platform-list-chat-items-list button[data-identity-id]');
	const itemCount = await chatItems.count();
	const chatData: ChatData[] = [];

	for (let i = 0; i < itemCount; i++) {
		const chatItem = chatItems.nth(i);
		const identityId = await chatItem.getAttribute('data-identity-id');
		const ticketId = await chatItem.getAttribute('data-ticket-id');

		// Extract customer name
		const customerNameElement = page.locator(
			`#platform-list-chat-item-customer-name-${identityId}`
		);
		const customerName = (await customerNameElement.textContent()) || '';

		// Extract owner name from badge
		const ownerBadgeElement = page.locator(`#platform-list-chat-item-owner-badge-${identityId}`);
		const ownerText = (await ownerBadgeElement.textContent()) || '';
		const ownerName = ownerText.trim(); // Remove icon and extract name

		// Extract channel name from badge
		const channelBadgeElement = page.locator(
			`#platform-list-chat-item-channel-badge-${identityId}`
		);
		const channelText = (await channelBadgeElement.textContent()) || '';
		const channelName = channelText.trim(); // Remove icon and extract name

		// Extract platform from platform icon
		const platformIconElement = page.locator(
			`#platform-list-chat-item-platform-icon-${identityId}`
		);
		const platformAlt = (await platformIconElement.getAttribute('alt')) || '';
		const platformName = platformAlt.replace(' icon', '').toUpperCase();

		// Check for unread status
		const unreadElement = page.locator(`#platform-list-chat-item-unread-count-${identityId}`);
		const fallbackUnreadElement = page.locator(
			`#platform-list-chat-item-fallback-unread-count-${identityId}`
		);
		const hasUnread =
			(await unreadElement.isVisible()) || (await fallbackUnreadElement.isVisible());

		chatData.push({
			identityId: identityId || '',
			customerName: customerName.trim(),
			ownerName: ownerName,
			channelName: channelName,
			platformName: platformName,
			hasUnread: hasUnread,
			ticketId: ticketId || ''
		});
	}

	console.log(`  → Extracted ${chatData.length} chat records from ${tabId} tab`);
	return chatData;
}

/**
 * Collect comprehensive filter test data from all tabs
 */
async function collectFilterTestData(page): Promise<FilterTestData> {
	console.log('\n--- Collecting filter test data from all tabs ---');

	const myAssigned = await extractChatDataFromTab(page, 'my-assigned');
	const open = await extractChatDataFromTab(page, 'open');
	const otherAssigned = await extractChatDataFromTab(page, 'others-assigned');

	const allChats = [...myAssigned, ...open, ...otherAssigned];

	// Extract unique values for filter testing
	const uniqueOwners = [...new Set(allChats.map((chat) => chat.ownerName).filter((name) => name))];
	const uniqueChannels = [
		...new Set(allChats.map((chat) => chat.channelName).filter((name) => name))
	];
	const uniquePlatforms = [
		...new Set(allChats.map((chat) => chat.platformName).filter((name) => name))
	];

	console.log(`Total chats collected: ${allChats.length}`);
	console.log(`Unique owners: ${uniqueOwners.length} (${uniqueOwners.join(', ')})`);
	console.log(`Unique channels: ${uniqueChannels.length} (${uniqueChannels.join(', ')})`);
	console.log(`Unique platforms: ${uniquePlatforms.length} (${uniquePlatforms.join(', ')})`);

	return {
		myAssigned,
		open,
		otherAssigned,
		allChats,
		uniqueOwners,
		uniqueChannels,
		uniquePlatforms
	};
}

/**
 * Open the filter panel
 */
async function openFilterPanel(page) {
	console.log('Opening filter panel...');

	const filterButton = page.locator('#platform-list-filter-button');
	await expect(filterButton).toBeVisible();
	await filterButton.click();

	// Wait for filter panel to open
	await expect(page.locator('#filter-panel-container')).toBeVisible();
	console.log('✓ Filter panel opened successfully');
}

/**
 * Apply and close the filter panel
 */
async function applyFilterPanel(page) {
	console.log('Applying the filter panel...');

	const filterButton = page.locator('#filter-panel-apply-button');
	await expect(filterButton).toBeVisible();
	await filterButton.click();

	console.log('✓ Filter panel applied successfully');
}

/**
 * Close the filter panel
 */
async function closeFilterPanel(page) {
	console.log('Closing filter panel...');

	const closeButton = page.locator('#filter-panel-close-button');
	await expect(closeButton).toBeVisible();
	await closeButton.click();

	// Wait for filter panel to close
	await expect(page.locator('#filter-panel-container')).not.toBeVisible();
	console.log('✓ Filter panel closed successfully');
}

/**
 * Clear all filters
 */
async function clearAllFilters(page) {
	console.log('Clearing all filters...');

	await openFilterPanel(page);

	const clearButton = page.locator('#filter-panel-clear-button');
	await expect(clearButton).toBeVisible();
	await clearButton.click();

	await applyFilterPanel(page);
	console.log('✓ All filters cleared successfully');
}

test.describe('Chat Center Filter Panel Functionality', () => {
	test('should filter chat items correctly using all filter options with round-trip testing', async ({
		page
	}) => {
		// Step 1: Authentication and navigation to chat center
		await performLoginWithRedirectHandling(page);
		await expect(page).toHaveURL('/chat_center');
		console.log('✓ Successfully navigated to chat center');

		// Step 2: Wait for PlatformIdentityList component to load
		await waitForPlatformIdentityListLoad(page);

		// Step 3: Collect comprehensive filter test data
		const testData = await collectFilterTestData(page);

		if (testData.allChats.length === 0) {
			console.log('⚠️ No chat data available for filter testing');
			return;
		}

		// Step 4: Test unread filter functionality
		console.log('\n--- Testing unread filter functionality ---');
		await openFilterPanel(page);

		// Apply "With Unread only" filter
		const unreadFilter = page.locator('#filter-panel-unread-checkbox-unread');
		await expect(unreadFilter).toBeVisible();
		await unreadFilter.click();

		await applyFilterPanel(page);

		// Verify only chats with unread messages are visible
		const expectedUnreadChats = testData.allChats.filter((chat) => chat.hasUnread);
		console.log(`Expected ${expectedUnreadChats.length} chats with unread messages`);

		// Count visible chat items and verify they match expected unread chats
		const visibleChatItems = page.locator(
			'#platform-list-chat-items-list button[data-identity-id]'
		);
		const visibleCount = await visibleChatItems.count();

		if (expectedUnreadChats.length > 0) {
			expect(visibleCount).toBeGreaterThan(0);
			console.log(`✓ Unread filter working: ${visibleCount} chats visible`);
		}

		// Reset filters
		await clearAllFilters(page);

		// Step 5: Test owner filter functionality
		if (testData.uniqueOwners.length > 0) {
			console.log('\n--- Testing owner filter functionality ---');

			for (const ownerName of testData.uniqueOwners.slice(0, 2)) {
				// Test first 2 owners
				console.log(`Testing owner filter for: ${ownerName}`);

				await openFilterPanel(page);

				// Find and click the owner filter
				const ownerFilter = page.locator(`#filter-panel-owner-checkbox-${ownerName}`);
				if (await ownerFilter.isVisible()) {
					await ownerFilter.click();
					await applyFilterPanel(page);

					// Verify filtering results
					const expectedOwnerChats = testData.allChats.filter(
						(chat) => chat.ownerName === ownerName
					);
					console.log(`Expected ${expectedOwnerChats.length} chats for owner ${ownerName}`);

					const visibleCount = await visibleChatItems.count();
					if (expectedOwnerChats.length > 0) {
						expect(visibleCount).toBeGreaterThan(0);
						console.log(`✓ Owner filter working for ${ownerName}: ${visibleCount} chats visible`);
					}

					// Reset filters
					await clearAllFilters(page);
					// Ensure filter panel is closed after clearing
					await expect(page.locator('#filter-panel-container')).not.toBeVisible();
				} else {
					console.log(`  → Skipping owner filter test for ${ownerName} as it's not visible`);
					closeFilterPanel(page);
					// Ensure filter panel is closed before opening
					await expect(page.locator('#filter-panel-container')).not.toBeVisible();
					await page.waitForTimeout(1000); // Small delay to ensure panel is fully closed
				}
			}
		}

		// Step 6: Test channel filter functionality
		if (testData.uniqueChannels.length > 0) {
			console.log('\n--- Testing channel filter functionality ---');

			for (const channelName of testData.uniqueChannels.slice(0, 2)) {
				// Test first 2 channels
				console.log(`Testing channel filter for: ${channelName}`);

				await openFilterPanel(page);

				// Find and click the channel filter (replace | with - for ID)
				const channelId = `${testData.uniquePlatforms[0] || 'UNKNOWN'}|${channelName}`.replace(
					'|',
					'-'
				);
				const channelFilter = page.locator(`#filter-panel-channel-checkbox-${channelId}`);

				if (await channelFilter.isVisible()) {
					await channelFilter.click();
					await applyFilterPanel(page);

					// Verify filtering results
					const expectedChannelChats = testData.allChats.filter(
						(chat) => chat.channelName === channelName
					);
					console.log(`Expected ${expectedChannelChats.length} chats for channel ${channelName}`);

					const visibleCount = await visibleChatItems.count();
					if (expectedChannelChats.length > 0) {
						expect(visibleCount).toBeGreaterThan(0);
						console.log(
							`✓ Channel filter working for ${channelName}: ${visibleCount} chats visible`
						);
					}

					// Reset filters
					await clearAllFilters(page);
					// Ensure filter panel is closed after clearing
					await expect(page.locator('#filter-panel-container')).not.toBeVisible();
				} else {
					console.log(`  → Skipping channel filter test for ${channelName} as it's not visible`);
					closeFilterPanel(page);
					// Ensure filter panel is closed before opening
					await expect(page.locator('#filter-panel-container')).not.toBeVisible();
					await page.waitForTimeout(1000); // Small delay to ensure panel is fully closed
				}
			}
		}

		// Step 7: Final verification - ensure all chats are visible after clearing filters
		console.log('\n--- Final verification: all chats visible after filter reset ---');
		const finalVisibleCount = await visibleChatItems.count();
		console.log(`Final visible chat count: ${finalVisibleCount}`);

		// The final count should be reasonable (may not match exactly due to tab switching)
		expect(finalVisibleCount).toBeGreaterThan(0);

		console.log('\n🎉 Filter panel functionality test completed successfully!');
	});
});
