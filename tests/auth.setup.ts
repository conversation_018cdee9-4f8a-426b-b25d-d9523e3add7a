import { test as setup } from '@playwright/test';

const authFile = 'playwright/.auth/user.json';

setup('authenticate', async ({ page }) => {
  // Visit the login page
  await page.goto('/login');
  
  // Wait for page to load
  await page.waitForSelector('input[name="username"]');
  
  // Fill in the login form
  await page.fill('input[name="username"]', 'testuser1');
  await page.fill('input[name="password"]', 'Testpass@1');
  
  // Submit the form
  await page.click('button[type="submit"]');

  // Wait for redirect to main page
  await page.waitForURL('/chat_center');
  
  // Save the authentication state
  await page.context().storageState({ path: authFile });
});