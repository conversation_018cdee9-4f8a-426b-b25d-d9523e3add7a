/**
 * End-to-End Test: Chat Center Message Input Disabled State Workflow
 *
 * This comprehensive test validates that MessageInput.svelte elements become properly
 * disabled when selecting chats from "Open" or "Other Assigned" tabs, ensuring users
 * cannot send messages or attach files when viewing chats they are not assigned to handle.
 *
 * SVELTEKIT PAGES TESTED:
 * - /chat_center (+page.svelte) - Main chat center interface with platform identity list
 *   └── Loads platform identities via +page.server.ts load function
 *   └── Integrates PlatformIdentityList and ConversationView components
 *
 * SVELTE COMPONENTS TESTED:
 * - PlatformIdentityList.svelte - Chat conversation list with tab navigation
 *   └── Tab buttons: platform-list-chat-tab-{tab.id} (line 736)
 *   └── Chat items: platform-list-chat-item-{identity.id} (line 764)
 *   └── Provides tab filtering based on ticket ownership and status
 * - ConversationView.svelte - Conversation display and message input container
 *   └── Controls canSendMessage based on latest_ticket_owner_id === currentLoginUser.id (line 445)
 *   └── Sets messageInputDisabled = loading || !canSendMessage (line 446)
 *   └── Passes disabled and canSendMessage props to MessageInput (lines 477-481)
 * - MessageInput.svelte - Message input form with file attachment capabilities
 *   └── File attachment button: message-input-attachment-button (line 301)
 *   └── Message textarea: message-input-textarea (line 357)
 *   └── Send button: message-input-send-button (line 372)
 *   └── File input: message-input-file-input (line 396)
 *   └── All elements disabled when disabled || !canSendMessage is true
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to chat center page (/chat_center)
 * 2. Switch to "Open" tab and select a chat - verify MessageInput elements are disabled
 * 3. Switch to "Other Assigned" tab and select a chat - verify MessageInput elements are disabled
 * 4. Switch to "My Assigned" tab and select a chat - verify MessageInput elements are enabled
 * 5. Round-trip testing to restore original state and verify state persistence
 *
 * DISABLED STATE LOGIC:
 * MessageInput elements are disabled when the current user is not the ticket owner:
 * - "Open" tab: status === 'open' (no specific owner)
 * - "Other Assigned" tab: status !== 'open' && owner !== userFullName
 * - "My Assigned" tab: status === 'assigned' && owner === userFullName (enabled)
 *
 * ID SELECTOR STRATEGY:
 * Uses component-name- prefix patterns for reliable element targeting:
 * - Tab selectors: platform-list-chat-tab-{tab-id}
 * - Chat item selectors: platform-list-chat-item-{identity-id}
 * - MessageInput selectors: message-input-{element-type}
 *
 * LANGUAGE-AGNOSTIC TESTING:
 * Uses DOM attribute inspection (disabled states, visibility) instead of text-based
 * detection to ensure test reliability across different languages and UI states.
 */

import { test, expect, type Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from './utils/auth.utils';


/**
 * Wait for the PlatformIdentityList component to fully load
 * Ensures chat tabs and content are ready for interaction
 */
async function waitForPlatformIdentityListLoad(page: Page): Promise<void> {
	console.log('Waiting for PlatformIdentityList component to load...');
	
	// Wait for the tab container to be visible
	await expect(page.locator('#platform-list-chat-tabs-container')).toBeVisible({ timeout: 15000 });
	
	// Wait for at least one tab to be present
	await expect(page.locator('#platform-list-chat-tabs button').first()).toBeVisible({ timeout: 10000 });
	
	// Wait for the content container to be ready
	await expect(page.locator('#platform-list-chat-content-container')).toBeVisible({ timeout: 10000 });
	
	console.log('✓ PlatformIdentityList component loaded successfully');
}

/**
 * Switch to a specific tab and verify the switch was successful
 * @param page - Playwright page object
 * @param tabId - The tab ID to switch to (e.g., 'open', 'others-assigned', 'my-assigned')
 * @returns Promise<boolean> - true if tab has content, false if empty
 */
async function switchToTab(page: Page, tabId: string): Promise<boolean> {
	console.log(`Switching to tab: ${tabId}`);
	
	// Click the tab button using the unique ID
	const tabButton = page.locator(`#platform-list-chat-tab-${tabId}`);
	await expect(tabButton).toBeVisible({ timeout: 10000 });
	await tabButton.click();
	await page.waitForTimeout(1500); // Allow tab content to load
	
	// Verify tab is now active using aria-selected attribute
	await expect(tabButton).toHaveAttribute('aria-selected', 'true');
	console.log(`✓ Successfully switched to ${tabId} tab`);
	
	// Check if tab has content
	const emptyState = page.locator('#platform-list-empty-state');
	const hasContent = !(await emptyState.isVisible());
	
	if (!hasContent) {
		console.log(`  → Tab ${tabId} is empty, no chats available`);
	}
	
	return hasContent;
}

/**
 * Select the first available chat item in the current tab
 * @param page - Playwright page object
 * @returns Promise<string | null> - The identity ID of the selected chat, or null if no chats available
 */
async function selectFirstChatItem(page: Page): Promise<string | null> {
	console.log('Selecting first available chat item...');
	
	// Wait for chat items to be present
	const chatItems = page.locator('#platform-list-chat-items-list button[data-testid="chat-item"]');
	const chatCount = await chatItems.count();
	
	if (chatCount === 0) {
		console.log('  → No chat items available in current tab');
		return null;
	}
	
	// Get the first chat item and extract its identity ID
	const firstChatItem = chatItems.first();
	const identityId = await firstChatItem.getAttribute('data-identity-id');
	
	if (!identityId) {
		console.log('  → Could not extract identity ID from chat item');
		return null;
	}
	
	// Click the chat item
	await firstChatItem.click();
	await page.waitForTimeout(2000); // Allow conversation to load
	
	console.log(`✓ Selected chat item with identity ID: ${identityId}`);
	return identityId;
}

/**
 * Verify that MessageInput elements are in the expected disabled state
 * @param page - Playwright page object
 * @param shouldBeDisabled - Whether elements should be disabled (true) or enabled (false)
 */
async function verifyMessageInputDisabledState(page: Page, shouldBeDisabled: boolean): Promise<void> {
	const stateDescription = shouldBeDisabled ? 'disabled' : 'enabled';
	console.log(`Verifying MessageInput elements are ${stateDescription}...`);
	
	// Define the elements to check
	const elementsToCheck = [
		{ id: 'message-input-attachment-button', name: 'File attachment button' },
		{ id: 'message-input-textarea', name: 'Message textarea' }
	];
	
	// Check each element's disabled state
	for (const element of elementsToCheck) {
		const locator = page.locator(`#${element.id}`);
		
		// Ensure element is visible first
		await expect(locator).toBeVisible({ timeout: 10000 });
		
		// Check disabled attribute
		if (shouldBeDisabled) {
			await expect(locator).toHaveAttribute('disabled', '');
			console.log(`  ✓ ${element.name} is properly disabled`);
		} else {
			await expect(locator).not.toHaveAttribute('disabled', '');
			console.log(`  ✓ ${element.name} is properly enabled`);
		}
	}
	
	console.log(`✓ All MessageInput elements are correctly ${stateDescription}`);
}

test.describe('Chat Center Message Input Disabled State', () => {
	test('should disable MessageInput elements when selecting chats from Open or Other Assigned tabs', async ({ page }) => {
		// Step 1: Authentication and navigation to chat center
		await performLoginWithRedirectHandling(page);
		await expect(page).toHaveURL('/chat_center');
		console.log('✓ Successfully navigated to chat center');
		
		// Step 2: Wait for PlatformIdentityList component to load
		await waitForPlatformIdentityListLoad(page);
		
		// Step 3: Test "Open" tab - elements should be disabled
		console.log('\n--- Testing Open tab (should disable MessageInput) ---');
		const openTabHasContent = await switchToTab(page, 'open');
		
		if (openTabHasContent) {
			const openChatId = await selectFirstChatItem(page);
			if (openChatId) {
				await verifyMessageInputDisabledState(page, true);
			} else {
				console.log('  → Skipping MessageInput verification - no chats available in Open tab');
			}
		} else {
			console.log('  → Skipping Open tab test - no content available');
		}
		
		// Step 4: Test "Other Assigned" tab - elements should be disabled
		console.log('\n--- Testing Other Assigned tab (should disable MessageInput) ---');
		const otherAssignedTabHasContent = await switchToTab(page, 'others-assigned');
		
		if (otherAssignedTabHasContent) {
			const otherAssignedChatId = await selectFirstChatItem(page);
			if (otherAssignedChatId) {
				await verifyMessageInputDisabledState(page, true);
			} else {
				console.log('  → Skipping MessageInput verification - no chats available in Other Assigned tab');
			}
		} else {
			console.log('  → Skipping Other Assigned tab test - no content available');
		}
		
		// Step 5: Test "My Assigned" tab - elements should be enabled (round-trip test)
		console.log('\n--- Testing My Assigned tab (should enable MessageInput) ---');
		const myAssignedTabHasContent = await switchToTab(page, 'my-assigned');
		
		if (myAssignedTabHasContent) {
			const myAssignedChatId = await selectFirstChatItem(page);
			if (myAssignedChatId) {
				await verifyMessageInputDisabledState(page, false);
			} else {
				console.log('  → Skipping MessageInput verification - no chats available in My Assigned tab');
			}
		} else {
			console.log('  → Skipping My Assigned tab test - no content available');
		}
		
		// Step 6: Verify state persistence by switching back to a restricted tab
		console.log('\n--- Testing state persistence (round-trip verification) ---');
		if (openTabHasContent) {
			await switchToTab(page, 'open');
			const persistenceChatId = await selectFirstChatItem(page);
			if (persistenceChatId) {
				await verifyMessageInputDisabledState(page, true);
				console.log('✓ Disabled state persists correctly after tab switching');
			}
		} else if (otherAssignedTabHasContent) {
			await switchToTab(page, 'others-assigned');
			const persistenceChatId = await selectFirstChatItem(page);
			if (persistenceChatId) {
				await verifyMessageInputDisabledState(page, true);
				console.log('✓ Disabled state persists correctly after tab switching');
			}
		}
		
		console.log('\n🎉 Message Input disabled state test completed successfully!');
	});
});
