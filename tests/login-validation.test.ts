import { test, expect } from '@playwright/test';

test.describe('Login Form Validation', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
  });

  test.describe('Username validation', () => {
		// test('should require username for form submission', async ({ page }) => {
		// 	await page.fill('input[name="password"]', 'testpassword');

		// 	// Form should be disabled when username is empty
		// 	const submitButton = page.locator('button[type="submit"]');
		// 	await expect(submitButton).toBeDisabled();
		// });

		test('should filter invalid characters in username', async ({ page }) => {
			const usernameInput = page.locator('input[name="username"]');

			// Test various invalid characters - should be filtered out
			await usernameInput.fill('test@user');
			await expect(usernameInput).toHaveValue('testuser');

			await usernameInput.fill('test#user');
			await expect(usernameInput).toHaveValue('testuser');

			await usernameInput.fill('test user');
			await expect(usernameInput).toHaveValue('testuser');

			await usernameInput.fill('test$user');
			await expect(usernameInput).toHaveValue('testuser');

			await usernameInput.fill('test%user');
			await expect(usernameInput).toHaveValue('testuser');

			await usernameInput.fill('test&user');
			await expect(usernameInput).toHaveValue('testuser');

			await usernameInput.fill('test*user');
			await expect(usernameInput).toHaveValue('testuser');
		});

		test('should allow valid characters in username', async ({ page }) => {
			const usernameInput = page.locator('input[name="username"]');

			// Test valid characters: alphanumeric, dots, underscores, hyphens
			await usernameInput.fill('test.user_name-123');
			await expect(usernameInput).toHaveValue('test.user_name-123');

			await usernameInput.fill('user123');
			await expect(usernameInput).toHaveValue('user123');

			await usernameInput.fill('test_user');
			await expect(usernameInput).toHaveValue('test_user');

			await usernameInput.fill('test-user');
			await expect(usernameInput).toHaveValue('test-user');

			await usernameInput.fill('test.user');
			await expect(usernameInput).toHaveValue('test.user');
		});

		test('should handle mixed valid and invalid characters with 20 character limit', async ({
			page
		}) => {
			const usernameInput = page.locator('input[name="username"]');

			// Test longer string that exceeds 20 characters
			await usernameInput.fill('verylongusername.with_hyphens-and.dots123456789');

			// Wait for filtering and length limit to complete
			await page.waitForFunction(() => {
				const input = document.querySelector('input[id="username"]') as HTMLInputElement;
				return input.value === 'verylongusername.wit';
			});

			await expect(usernameInput).toHaveValue('verylongusername.wit');
		});

		// test('should enforce 3-character minimum length requirement', async ({ page }) => {
		// 	const usernameInput = page.locator('input[name="username"]');
		// 	const passwordInput = page.locator('input[name="password"]');
		// 	const submitButton = page.locator('button[type="submit"]');

		// 	// Fill password to isolate username validation
		// 	await passwordInput.fill('testpassword');

		// 	// Test usernames shorter than 3 characters - button should be disabled
		// 	await usernameInput.fill('ab');
		// 	await expect(submitButton).toBeDisabled();

		// 	await usernameInput.fill('a');
		// 	await expect(submitButton).toBeDisabled();

		// 	await usernameInput.fill('');
		// 	await expect(submitButton).toBeDisabled();

		// 	// Test valid 3-character username - button should be enabled
		// 	await usernameInput.fill('abc');
		// 	await expect(submitButton).toBeEnabled();

		// 	// Test longer valid username - button should be enabled
		// 	await usernameInput.fill('abcd');
		// 	await expect(submitButton).toBeEnabled();
		// });

		test('should show validation error for usernames starting with special characters', async ({
			page
		}) => {
			const usernameInput = page.locator('input[name="username"]');
			const passwordInput = page.locator('input[name="password"]');
			const submitButton = page.locator('button[type="submit"]');

			// Fill password to isolate username validation
			await passwordInput.fill('testpassword');

			// Test usernames starting with dot, underscore, or hyphen
			await usernameInput.fill('.testuser');
			// await expect(submitButton).toBeDisabled();
			await expect(
				page.locator('div[id="username-error"]')
			).toBeVisible();

			await usernameInput.fill('_testuser');
			// await expect(submitButton).toBeDisabled();
			await expect(
				page.locator('div[id="username-error"]')
			).toBeVisible();

			await usernameInput.fill('-testuser');
			// await expect(submitButton).toBeDisabled();
			await expect(
				page.locator('div[id="username-error"]')
			).toBeVisible();

			// Valid username starting with letter should work
			await usernameInput.fill('testuser');
			await expect(submitButton).toBeEnabled();
			await expect(
				page.locator('div[id="username-error"]')
			).not.toBeVisible();
		});

		test('should disable form for usernames with consecutive special characters', async ({
			page
		}) => {
			const usernameInput = page.locator('input[name="username"]');
			const passwordInput = page.locator('input[name="password"]');
			const submitButton = page.locator('button[type="submit"]');

			// Fill password to isolate username validation
			await passwordInput.fill('testpassword');

			// Test consecutive hyphens, dots, underscores (should be filtered to single character)
			await usernameInput.fill('test--user');
			await expect(page.locator('input[name="username"]')).toHaveValue('test-user');
			await expect(submitButton).toBeEnabled();

			await usernameInput.fill('test..user');
			await expect(page.locator('input[name="username"]')).toHaveValue('test.user');
			await expect(submitButton).toBeEnabled();

			await usernameInput.fill('test__user');
			await expect(page.locator('input[name="username"]')).toHaveValue('test_user');
			await expect(submitButton).toBeEnabled();

			// Valid username should work
			await usernameInput.fill('test.user');
			await expect(submitButton).toBeEnabled();
		});

		// test('should disable form for blocked words in username', async ({ page }) => {
		// 	const usernameInput = page.locator('input[name="username"]');
		// 	const passwordInput = page.locator('input[name="password"]');
		// 	const submitButton = page.locator('button[type="submit"]');

		// 	// Fill password to isolate username validation
		// 	await passwordInput.fill('testpassword');

		// 	// Test blocked words (should disable form but not show error)
		// 	const blockedWords = ['union', 'select', 'drop', 'exec', 'system32'];

		// 	for (const word of blockedWords) {
		// 		await usernameInput.fill(word);
		// 		await expect(submitButton).toBeDisabled();

		// 		// Also test as part of username with separators
		// 		await usernameInput.fill(`test-${word}`);
		// 		await expect(submitButton).toBeDisabled();

		// 		await usernameInput.fill(`test.${word}`);
		// 		await expect(submitButton).toBeDisabled();
		// 	}

		// 	// Valid username should work
		// 	await usernameInput.fill('testuser');
		// 	await expect(submitButton).toBeEnabled();
		// });
	});

	test.describe('Password validation', () => {
		// test('should require password for form submission', async ({ page }) => {
		// 	await page.fill('input[id="username"]', 'testuser1');

		// 	// Form should be disabled when password is empty
		// 	const submitButton = page.locator('button[type="submit"]');
		// 	await expect(submitButton).toBeDisabled();
		// });

		test('should accept any password characters', async ({ page }) => {
			const passwordInput = page.locator('input[name="password"]');

			// Test special characters in password
			await passwordInput.fill('password@123!#$%');
			await expect(passwordInput).toHaveValue('password@123!#$%');
		});

		test('should mask password input by default', async ({ page }) => {
			const passwordInput = page.locator('input[name="password"]');

			await expect(passwordInput).toHaveAttribute('type', 'password');
		});

		test('should toggle password visibility', async ({ page }) => {
			const passwordInput = page.locator('input[name="password"]');
			const toggleButton = page.locator('button[aria-label*="password"]');

			// Initially password should be masked
			await expect(passwordInput).toHaveAttribute('type', 'password');

			// Click toggle to show password
			await toggleButton.click();
			await expect(passwordInput).toHaveAttribute('type', 'text');

			// Click toggle again to hide password
			await toggleButton.click();
			await expect(passwordInput).toHaveAttribute('type', 'password');
		});

		test('should respect 50 character limit', async ({ page }) => {
			const passwordInput = page.locator('input[name="password"]');

			// Test with password longer than 50 characters
			const longPassword = 'a'.repeat(60);
			await passwordInput.fill(longPassword);

			// Should be limited to 50 characters
			await expect(passwordInput).toHaveValue('a'.repeat(50));
		});
	});

	test.describe('Form submission validation', () => {
		// test('should disable submit button with empty form', async ({ page }) => {
		// 	const submitButton = page.locator('button[type="submit"]');

		// 	// Submit button should be disabled when form is empty
		// 	await expect(submitButton).toBeDisabled();
		// 	await expect(submitButton).toHaveClass(/cursor-not-allowed/);
		// 	await expect(submitButton).toHaveClass(/opacity-50/);
		// });

		// test('should disable submit button with only username', async ({ page }) => {
		// 	await page.fill('input[name="username"]', 'testuser1');
		// 	const submitButton = page.locator('button[type="submit"]');

		// 	// Submit button should remain disabled without password
		// 	await expect(submitButton).toBeDisabled();
		// });

		// test('should disable submit button with only password', async ({ page }) => {
		// 	await page.fill('input[name="password"]', 'testpassword');
		// 	const submitButton = page.locator('button[type="submit"]');

		// 	// Submit button should remain disabled without username
		// 	await expect(submitButton).toBeDisabled();
		// });

		test('should enable submit button with valid data', async ({ page }) => {
			await page.fill('input[name="username"]', 'testuser1');
			await page.fill('input[name="password"]', 'testpassword');
			const submitButton = page.locator('button[type="submit"]');

			// Submit button should be enabled with valid data
			await expect(submitButton).toBeEnabled();
			await expect(submitButton).not.toHaveClass(/cursor-not-allowed/);
			await expect(submitButton).not.toHaveClass(/opacity-50/);
		});

		test('should show login error on failed authentication', async ({ page }) => {
			await page.fill('input[name="username"]', 'invaliduser');
			await page.fill('input[name="password"]', 'invalidpassword');

			await page.click('button[type="submit"]');

			// Wait for the form submission to complete and error to appear
			// Note: The component shows translated error messages based on current language
			await expect(page.locator('div[id="login-error"]')).toBeVisible();
		});

		test('should hide login error when user starts typing', async ({ page }) => {
			// First trigger a login error
			await page.fill('input[name="username"]', 'invaliduser');
			await page.fill('input[name="password"]', 'invalidpassword');
			await page.click('button[type="submit"]');

			// Wait for error to appear
			await expect(page.locator('div[id="login-error"]')).toBeVisible();

			// Start typing in username field
			await page.fill('input[name="username"]', 'newuser');

			// Error should be hidden
			await expect(page.locator('div[id="login-error"]')).not.toBeVisible();
		});
	});

	test.describe('Real-time validation', () => {
		test('should validate username in real-time during input', async ({ page }) => {
			const usernameInput = page.locator('input[name="username"]');
			const passwordInput = page.locator('input[name="password"]');
			const submitButton = page.locator('button[type="submit"]');

			// Fill password to isolate username validation
			await passwordInput.fill('testpassword');

			// Type invalid username (starts with dot)
			await usernameInput.fill('.test');

			// Wait for validation to process
			// await page.waitForFunction(() => {
			// 	const submitBtn = document.querySelector('button[type="submit"]') as HTMLButtonElement;
			// 	return submitBtn.disabled;
			// });

			// await expect(submitButton).toBeDisabled();
			await expect(usernameInput).toHaveClass(/border-red-500/);

			// Fix username
			await usernameInput.fill('test');

			// Wait for validation to process
			await page.waitForFunction(() => {
				const submitBtn = document.querySelector('button[type="submit"]') as HTMLButtonElement;
				return !submitBtn.disabled;
			});

			await expect(submitButton).toBeEnabled();
			await expect(usernameInput).not.toHaveClass(/border-red-500/);
		});

		test('should clear validation errors when typing valid input', async ({ page }) => {
			const usernameInput = page.locator('input[name="username"]');

			// Start with invalid username
			await usernameInput.fill('.invalid');
			await expect(
				page.locator('div[id="username-error"]')
			).toBeVisible();

			// Type valid username
			await usernameInput.fill('validuser');

			// Wait for validation to process and error to disappear
			await page.waitForFunction(() => {
				const errorMsg = document.querySelector(
					'div[id="username-error"]'
				);
				return !errorMsg || !errorMsg.isConnected;
			});

			// Error should disappear
			await expect(
				page.locator('div[id="username-error"]')
			).not.toBeVisible();
		});
	});

	test.describe('Edge cases', () => {
		test('should handle rapid character input', async ({ page }) => {
			const usernameInput = page.locator('input[name="username"]');

			// Rapid typing with mixed characters
			await usernameInput.fill('t@e#s$t%u^s&e*r');

			// Wait for input processing and filtering
			await page.waitForFunction(() => {
				const input = document.querySelector('input[id="username"]') as HTMLInputElement;
				return input.value === 'testuser';
			});

			// Should filter out invalid characters
			await expect(usernameInput).toHaveValue('testuser');
		});

		test('should handle copy-paste with invalid characters', async ({ page }) => {
			const usernameInput = page.locator('input[name="username"]');

			// Focus the input field
			await usernameInput.focus();

			// Use Playwright's built-in paste method to simulate clipboard paste
			await page.evaluate(() => {
				// Set clipboard data
				const clipboardData = new DataTransfer();
				clipboardData.setData('text/plain', 'test@user#name$123');

				// Create and dispatch paste event
				const pasteEvent = new ClipboardEvent('paste', {
					clipboardData: clipboardData,
					bubbles: true,
					cancelable: true
				});

				const input = document.querySelector('input[id="username"]') as HTMLInputElement;
				input.dispatchEvent(pasteEvent);
			});

			// Wait for DOM update and check filtered characters
			await page.waitForFunction(() => {
				const input = document.querySelector('input[id="username"]') as HTMLInputElement;
				return input.value === 'testusername123';
			});

			// Should filter out invalid characters
			await expect(usernameInput).toHaveValue('testusername123');
		});

		test('should allow all special characters in password', async ({ page }) => {
			const passwordInput = page.locator('input[name="password"]');

			// Test with various special characters
			await passwordInput.fill('pass@word#123!$%^&*()');

			// Should accept the password with special characters
			await expect(passwordInput).toHaveValue('pass@word#123!$%^&*()');
		});
	});

	test.describe('Autofill functionality', () => {
		test('should enable submit button when autofill populates valid credentials', async ({ page }) => {
			const usernameInput = page.locator('input[name="username"]');
			const passwordInput = page.locator('input[name="password"]');
			const submitButton = page.locator('button[type="submit"]');

			// Initially form should be disabled
			// await expect(submitButton).toBeDisabled();

			// Wait for elements to be available
			await page.waitForSelector('input[name="username"]');
			await page.waitForSelector('input[name="password"]');

			// Simulate autofill by setting values directly on DOM elements without focus
			await page.evaluate(() => {
				const usernameEl = document.querySelector('input[name="username"]') as HTMLInputElement;
				const passwordEl = document.querySelector('input[name="password"]') as HTMLInputElement;
				
				// Check if elements exist before setting values
				if (usernameEl && passwordEl) {
					// Set values directly without triggering normal input events
					usernameEl.value = 'testuser123';
					passwordEl.value = 'testpassword456';
				}
			});

			// Wait for autofill detection mechanisms to kick in
			await page.waitForTimeout(600); // Allow polling and mutation observer to detect changes

			// Submit button should be enabled after autofill detection
			await expect(submitButton).toBeEnabled();
			
			// Form data should be synchronized
			await expect(usernameInput).toHaveValue('testuser123');
			await expect(passwordInput).toHaveValue('testpassword456');
		});

		test('should handle autofill with invalid username and keep form disabled', async ({ page }) => {
			const usernameInput = page.locator('input[name="username"]');
			const submitButton = page.locator('button[type="submit"]');

			// Wait for elements to be available
			await page.waitForSelector('input[name="username"]');
			await page.waitForSelector('input[name="password"]');

			// Simulate autofill with invalid username (starts with dot)
			await page.evaluate(() => {
				const usernameEl = document.querySelector('input[name="username"]') as HTMLInputElement;
				const passwordEl = document.querySelector('input[name="password"]') as HTMLInputElement;
				
				// Check if elements exist before setting values
				if (usernameEl && passwordEl) {
					usernameEl.value = '.invaliduser';
					passwordEl.value = 'validpassword';
				}
			});

			// Wait for autofill detection and validation
			await page.waitForTimeout(600);

			// Submit button should remain disabled due to invalid username
			// await expect(submitButton).toBeDisabled();
			
			// Username validation error should be shown
			await expect(usernameInput).toHaveClass(/border-red-500/);
			await expect(page.locator('div[id="username-error"]')).toBeVisible();
		});

		test('should filter invalid characters from autofilled username', async ({ page }) => {
			const usernameInput = page.locator('input[name="username"]');
			const submitButton = page.locator('button[type="submit"]');

			// Wait for elements to be available
			await page.waitForSelector('input[name="username"]');
			await page.waitForSelector('input[name="password"]');

			// Simulate autofill with username containing invalid characters
			await page.evaluate(() => {
				const usernameEl = document.querySelector('input[name="username"]') as HTMLInputElement;
				const passwordEl = document.querySelector('input[name="password"]') as HTMLInputElement;
				
				// Check if elements exist before setting values
				if (usernameEl && passwordEl) {
					usernameEl.value = 'test@user#name$123'; // Contains invalid characters
					passwordEl.value = 'validpassword';
				}
			});

			// Wait for autofill detection and filtering
			await page.waitForTimeout(600);

			// Username should be filtered to remove invalid characters
			await expect(usernameInput).toHaveValue('testusername123');
			
			// Submit button should be enabled with filtered valid username
			await expect(submitButton).toBeEnabled();
		});
	});

	test.describe('Language toggle functionality', () => {
		test('should default to Thai language', async ({ page }) => {
			const languageToggle = page.locator('button[aria-label="Toggle language"]');

			// Should show Thai flag and text by default
			await expect(languageToggle).toContainText('🇹🇭');
		});

		test('should toggle between Thai and English', async ({ page }) => {
			const languageToggle = page.locator('button[aria-label="Toggle language"]');

			// Initially should be Thai
			await expect(languageToggle).toContainText('🇹🇭');

			// Click to switch to English
			await languageToggle.click();
			await expect(languageToggle).toContainText('🇺🇸');

			// Click again to switch back to Thai
			await languageToggle.click();
			await expect(languageToggle).toContainText('🇹🇭');
		});

		test('should show translated error messages based on language', async ({ page }) => {
			const usernameInput = page.locator('input[name="username"]');
			const languageToggle = page.locator('button[aria-label="Toggle language"]');

			// Start with Thai (default) and trigger validation error
			await usernameInput.fill('.invalid');
			await expect(
				page.locator(
					'text=ชื่อผู้ใช้สามารถประกอบด้วย ตัวอักษร ตัวเลข จุด (.), ขีดล่าง (_), และขีดกลาง (-) เท่านั้น'
				)
			).toBeVisible();

			// Switch to English
			await languageToggle.click();
			await expect(
				page.locator(
					'text=Usernames may contain letters, numbers, dots (.), underscores (_), and hyphens (-) only.'
				)
			).toBeVisible();

			// Switch back to Thai
			await languageToggle.click();
			await expect(
				page.locator(
					'text=ชื่อผู้ใช้สามารถประกอบด้วย ตัวอักษร ตัวเลข จุด (.), ขีดล่าง (_), และขีดกลาง (-) เท่านั้น'
				)
			).toBeVisible();
		});
	});
});