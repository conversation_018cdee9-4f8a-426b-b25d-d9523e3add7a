/**
 * End-to-End Test: Chat Center Message Display After Selection Workflow
 *
 * This comprehensive test validates that chat messages are displayed correctly in MessageList.svelte
 * after selecting a chat from the PlatformIdentityList.svelte component, ensuring proper ticket group
 * rendering and MessageItem component visibility across different status tabs.
 *
 * SVELTEKIT PAGES TESTED:
 * - /chat_center (+page.svelte) - Main chat center interface with platform identity list
 *   └── Loads platform identities via +page.server.ts load function
 *   └── Integrates PlatformIdentityList and ConversationView components
 *
 * SVELTE COMPONENTS TESTED:
 * - PlatformIdentityList.svelte - Chat conversation list with tab navigation and chat selection
 *   └── Tab buttons: platform-list-chat-tab-{tab.id} (line 736)
 *   └── Chat items: platform-list-chat-item-{identity.id} (line 764)
 *   └── Ticket ID data attribute: data-ticket-id={identity['latest_ticket_id']} (line 772)
 *   └── Customer name data attribute: data-customer-name={identity.display_name || identity.platform_username} (line 773)
 * - MessageList.svelte - Message display component with ticket group organization
 *   └── Ticket groups: message-list-ticket-group-{ticketGroup.ticketId} (line 235)
 *   └── Message container: message-list-container (line 190)
 *   └── Sticky header: message-list-sticky-header (line 197)
 * - MessageItem.svelte - Individual message rendering component
 *   └── Message content: message-item-content-{message.id} (line 91)
 *   └── Message text: message-item-text-{message.id} (line 197)
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to chat center page (/chat_center)
 * 2. PlatformIdentityList component loading verification
 * 3. Random tab selection from available status tabs (My Assigned, My Closed, Open, Other Assigned)
 * 4. Random chat selection from the selected tab's chat items
 * 5. Ticket ID extraction from selected chat's data attributes
 * 6. MessageList ticket group verification using extracted ticket ID
 * 7. MessageItem component visibility verification within the ticket group
 * 8. Round-trip testing to ensure test environment consistency
 *
 * LANGUAGE-AGNOSTIC TESTING:
 * - Uses DOM attribute assertions (visibility, structure) instead of text-based checks
 * - Implements component-prefixed ID selectors following component-name- pattern
 * - Verifies functional behavior rather than UI text content
 * - Ensures test reliability across different language settings
 *
 * ROUND-TRIP TESTING PATTERN:
 * - Captures original state before interactions
 * - Performs test operations and verifications
 * - Validates that message display functionality works correctly
 * - Ensures no test environment pollution
 */

import { test, expect, type Page } from '@playwright/test';
import { performLoginWithRedirectHandling } from './utils/auth.utils';

test.use({ viewport: { width: 1920, height: 1080 } });

/**
 * Wait for the PlatformIdentityList component to fully load
 * Ensures chat tabs and content are ready for interaction
 */
async function waitForPlatformIdentityListLoad(page: Page): Promise<void> {
	console.log('Waiting for PlatformIdentityList component to load...');
	
	// Wait for the main component container
	await expect(page.locator('#platform-list-platform-identity-list')).toBeVisible({ timeout: 15000 });
	
	// Wait for tab navigation to be present
	await expect(page.locator('#platform-list-chat-tabs')).toBeVisible({ timeout: 10000 });
	
	// Wait for the content container to be ready
	await expect(page.locator('#platform-list-chat-content-container')).toBeVisible({ timeout: 10000 });
	
	// Allow initial tab content to load
	await page.waitForTimeout(2000);
	
	console.log('✓ PlatformIdentityList component loaded successfully');
}

/**
 * Switch to a specific tab and verify the switch was successful
 * @param page - Playwright page object
 * @param tabId - The tab ID to switch to (my-assigned, my-closed, open, others-assigned)
 * @returns Promise<boolean> - Whether the tab has content
 */
async function switchToTab(page: Page, tabId: string): Promise<boolean> {
	console.log(`Switching to tab: ${tabId}`);
	
	// Click the tab button using the unique ID
	const tabButton = page.locator(`#platform-list-chat-tab-${tabId}`);
	await expect(tabButton).toBeVisible({ timeout: 10000 });
	await tabButton.click();
	await page.waitForTimeout(1500); // Allow tab content to load
	
	// Verify tab is now active using aria-selected attribute
	await expect(tabButton).toHaveAttribute('aria-selected', 'true');
	console.log(`✓ Successfully switched to ${tabId} tab`);
	
	// Check if tab has content
	const emptyState = page.locator('#platform-list-empty-state');
	const hasContent = !(await emptyState.isVisible());
	
	if (!hasContent) {
		console.log(`  → Tab ${tabId} is empty, no chats available`);
	}
	
	return hasContent;
}

/**
 * Select a random chat item from the current tab and extract its ticket ID
 * @param page - Playwright page object
 * @returns Promise<{identityId: string, ticketId: string} | null> - The selected chat info or null if no chats available
 */
async function selectRandomChatItem(page: Page): Promise<{identityId: string, ticketId: string} | null> {
	console.log('Selecting random chat item...');
	
	// Wait for chat items to be present
	const chatItems = page.locator('#platform-list-chat-items-list button[data-testid="chat-item"]');
	const chatCount = await chatItems.count();
	
	if (chatCount === 0) {
		console.log('  → No chat items available in current tab');
		return null;
	}
	
	// Select a random chat item
	const randomIndex = Math.floor(Math.random() * chatCount);
	const selectedChatItem = chatItems.nth(randomIndex);
	
	// Extract identity ID and ticket ID from data attributes
	const identityId = await selectedChatItem.getAttribute('data-identity-id');
	const ticketId = await selectedChatItem.getAttribute('data-ticket-id');
	
	if (!identityId || !ticketId) {
		console.log('  → Could not extract identity ID or ticket ID from chat item');
		return null;
	}
	
	// Click the chat item
	await selectedChatItem.click();
	await page.waitForTimeout(2000); // Allow conversation to load
	
	console.log(`✓ Selected chat item with identity ID: ${identityId}, ticket ID: ${ticketId}`);
	return { identityId, ticketId };
}

/**
 * Select a random tab with content and return a chat from it
 * @param page - Playwright page object
 * @returns Promise<{tabId: string, identityId: string, ticketId: string} | null> - The selected tab and chat info
 */
async function selectRandomTabWithContent(page: Page): Promise<{tabId: string, identityId: string, ticketId: string} | null> {
	console.log('Selecting random tab with content...');
	
	const tabsToTry = ['my-assigned', 'my-closed', 'open', 'others-assigned'];
	
	// Shuffle the tabs array for random selection
	const shuffledTabs = [...tabsToTry].sort(() => Math.random() - 0.5);
	
	for (const tabId of shuffledTabs) {
		console.log(`Trying tab: ${tabId}`);
		
		const hasContent = await switchToTab(page, tabId);
		if (!hasContent) {
			console.log(`Tab ${tabId} has no content, trying next tab...`);
			continue;
		}
		
		const chatInfo = await selectRandomChatItem(page);
		if (chatInfo) {
			console.log(`✓ Successfully selected chat from ${tabId} tab`);
			return { tabId, ...chatInfo };
		}
	}
	
	throw new Error('No tabs with available chats found');
}

/**
 * Wait for MessageList component to load and verify ticket group display
 * @param page - Playwright page object
 * @param ticketId - The ticket ID to verify in the message list
 */
async function waitForMessageListLoad(page: Page, ticketId: string): Promise<void> {
	console.log(`Waiting for MessageList component to load with ticket ID: ${ticketId}...`);
	
	// Wait for the main message list container
	await expect(page.locator('#message-list-container')).toBeVisible({ timeout: 15000 });
	
	// Wait for the specific ticket group to be visible
	const ticketGroup = page.locator(`#message-list-ticket-group-${ticketId}`);
	await expect(ticketGroup).toBeVisible({ timeout: 10000 });
	
	console.log(`✓ MessageList component loaded with ticket group: ${ticketId}`);
}

/**
 * Verify that MessageItem components are visible within the ticket group
 * @param page - Playwright page object
 * @param ticketId - The ticket ID to check for message items
 */
async function verifyMessageItemsVisible(page: Page, ticketId: string): Promise<void> {
	console.log(`Verifying MessageItem components are visible for ticket ID: ${ticketId}...`);
	
	// Look for MessageItem components within the ticket group
	const ticketGroup = page.locator(`#message-list-ticket-group-${ticketId}`);
	const messageItems = ticketGroup.locator('[id^="message-item-content-"]');
	
	// Verify at least one message item is visible
	await expect(messageItems.first()).toBeVisible({ timeout: 10000 });
	
	const messageCount = await messageItems.count();
	console.log(`✓ Found ${messageCount} MessageItem component(s) in ticket group ${ticketId}`);
}

test.describe('Chat Center Message Display After Selection', () => {
	test('should display messages correctly after selecting a chat from platform identity list', async ({ page }) => {
		// Step 1: Authentication and navigation to chat center
		await performLoginWithRedirectHandling(page);
		await expect(page).toHaveURL('/chat_center');
		console.log('✓ Successfully navigated to chat center');

		// Step 2: Wait for PlatformIdentityList component to load
		await waitForPlatformIdentityListLoad(page);

		// Step 3: Select random tab with content and chat
		const selection = await selectRandomTabWithContent(page);
		if (!selection) {
			throw new Error('Could not find any tabs with available chats');
		}

		const { tabId, identityId, ticketId } = selection;
		console.log(`Selected chat from ${tabId} tab: identity ${identityId}, ticket ${ticketId}`);

		// Step 4: Wait for MessageList component to load with the selected ticket
		await waitForMessageListLoad(page, ticketId);

		// Step 5: Verify MessageItem components are visible within the ticket group
		await verifyMessageItemsVisible(page, ticketId);

		// Step 6: Round-trip testing - verify the selection is still active
		console.log('\n--- Performing round-trip testing ---');
		
		// Verify the selected chat item is still highlighted
		const selectedChatItem = page.locator(`#platform-list-chat-item-${identityId}`);
		await expect(selectedChatItem).toHaveAttribute('data-testid', 'chat-item-selected');
		
		// Verify the ticket group is still visible
		const ticketGroup = page.locator(`#message-list-ticket-group-${ticketId}`);
		await expect(ticketGroup).toBeVisible();
		
		console.log('✓ Round-trip testing completed - message display functionality verified');
		console.log('\n🎉 Chat Center message display after selection test completed successfully!');
	});
});
