/**
 * End-to-End Test: Account Settings Password Change Workflow
 *
 * This comprehensive test validates the complete password change workflow
 * in the account settings page, specifically focusing on updating the user password
 * using the unique HTML element IDs with "settings-user-profile-" prefix.
 *
 * SVELTEKIT PAGES TESTED:
 * - /settings/account (+page.svelte) - Account settings page with user profile management
 *   └── Loads user data via +page.server.ts load function
 *   └── Integrates UserProfile.svelte component for profile editing
 * - /login (+page.svelte) - Login page for password verification testing
 *   └── Validates old and new password authentication
 *
 * SVELTE COMPONENTS TESTED:
 * - UserProfile.svelte (/src/lib/components/settings/account/UserProfile.svelte)
 *   └── Handles password change modal and form validation
 *   └── Change password button: settings-user-profile-change-password-btn (line 531)
 *   └── Password modal: settings-user-profile-password-modal (line 793)
 *   └── Old password input: settings-user-profile-old-password (line 808)
 *   └── New password input: settings-user-profile-new-password (line 836)
 *   └── Confirm password input: settings-user-profile-confirm-password (line 908)
 *   └── Submit button: settings-user-profile-password-submit-btn (line 951)
 *   └── Cancel button: settings-user-profile-password-cancel-btn (line 942)
 * - Sidebar.svelte (/src/lib/components/sidebar.svelte)
 *   └── Logout functionality for testing password changes (line 862)
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to account settings page (/settings/account)
 * 2. Open password change modal via "Change Password" button
 * 3. Fill password change form with current, new, and confirm passwords
 * 4. Verify form validation and button state management
 * 5. Submit password change and verify success
 * 6. Logout using sidebar functionality
 * 7. Test old password fails on login attempt
 * 8. Test new password succeeds on login attempt
 * 9. Round-trip testing: revert password to original value
 * 10. Verify restoration of original password functionality
 *
 * DATA FLOW AND INTEGRATION POINTS:
 * - SvelteKit server-side password change form actions
 * - UserProfile component reactive form state management
 * - Password validation rules and confirmation matching
 * - Authentication system integration for login verification
 * - Cross-page authentication state management
 *
 * ROUND-TRIP TESTING PATTERN:
 * - Uses original password from auth.utils.ts for testing
 * - Performs password change operations and comprehensive verifications
 * - Reverts password back to original value to restore test environment
 * - Ensures no test environment pollution
 * - Validates authentication works with both old and new passwords
 *
 * ID SELECTOR STRATEGY:
 * All ID selectors use the "settings-user-profile-" prefix pattern established
 * in UserProfile.svelte component. Each selector references actual HTML elements
 * with documented line numbers for maintainability. Language-agnostic DOM
 * attribute assertions are used for robustness across different language settings.
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from './utils/auth.utils';

const USERNAME = 'admin';

// Extract original password from auth utils for round-trip testing
const ORIGINAL_PASSWORD = 'adminPW01!';
const TEST_PASSWORD = 'newTestPW02!';

/**
 * Utility function to navigate to account settings page
 * COMPONENT: Settings navigation and account page
 * ROUTE: /settings/account
 */
async function navigateToAccountSettings(page: Page) {
	console.log('Navigating to account settings page...');
	
	// Navigate directly to account settings
	await page.goto('/settings/account');
	await page.waitForTimeout(1000);
	
	// Verify we're on the account settings page
	await expect(page).toHaveURL('/settings/account');
	
	// Wait for UserProfile component to load
	const userProfileForm = page.locator('#settings-user-profile-form');
	await expect(userProfileForm).toBeVisible({ timeout: 10000 });
	
	console.log('✓ Successfully navigated to account settings page');
}

/**
 * Utility function to open password change modal
 * COMPONENT: UserProfile.svelte
 * ELEMENT: settings-user-profile-change-password-btn (line 531)
 */
async function openPasswordChangeModal(page: Page) {
	console.log('Opening password change modal...');
	
	const changePasswordButton = page.locator('#settings-user-profile-change-password-btn');
	await expect(changePasswordButton).toBeVisible({ timeout: 10000 });
	
	await changePasswordButton.click();
	await page.waitForTimeout(1000);
	
	// Verify modal is open
	const passwordModal = page.locator('#settings-user-profile-password-modal');
	await expect(passwordModal).toBeVisible({ timeout: 10000 });
	
	console.log('✓ Password change modal opened successfully');
}

/**
 * Utility function to fill password change form
 * COMPONENT: UserProfile.svelte
 * ELEMENTS: settings-user-profile-old-password, settings-user-profile-new-password, settings-user-profile-confirm-password
 */
async function fillPasswordChangeForm(page: Page, oldPassword: string, newPassword: string) {
	console.log(`Filling password change form...`);
	
	const oldPasswordInput = page.locator('#settings-user-profile-old-password');
	const newPasswordInput = page.locator('#settings-user-profile-new-password');
	const confirmPasswordInput = page.locator('#settings-user-profile-confirm-password');
	const submitButton = page.locator('#settings-user-profile-password-submit-btn');
	
	// Verify all inputs are visible
	await expect(oldPasswordInput).toBeVisible({ timeout: 10000 });
	await expect(newPasswordInput).toBeVisible({ timeout: 10000 });
	await expect(confirmPasswordInput).toBeVisible({ timeout: 10000 });
	
	// Verify submit button is initially disabled
	await expect(submitButton).toBeDisabled();
	console.log('  Submit button initially disabled');
	
	// Fill old password
	await oldPasswordInput.fill(oldPassword);
	await page.waitForTimeout(500);
	
	// Fill new password
	await newPasswordInput.fill(newPassword);
	await page.waitForTimeout(500);
	
	// Fill confirm password
	await confirmPasswordInput.fill(newPassword);
	await page.waitForTimeout(500);
	
	// Verify submit button becomes enabled
	await expect(submitButton).toBeEnabled();
	console.log('  Submit button enabled after filling all fields');
	
	// Verify input values
	await expect(oldPasswordInput).toHaveValue(oldPassword);
	await expect(newPasswordInput).toHaveValue(newPassword);
	await expect(confirmPasswordInput).toHaveValue(newPassword);
	
	console.log('✓ Password change form filled successfully');
}

/**
 * Utility function to submit password change form
 * COMPONENT: UserProfile.svelte
 * ELEMENT: settings-user-profile-password-submit-btn (line 951)
 */
async function submitPasswordChangeForm(page: Page) {
	console.log('Submitting password change form...');
	
	const submitButton = page.locator('#settings-user-profile-password-submit-btn');
	await expect(submitButton).toBeEnabled();
	
	// Click submit button
	await submitButton.click();
	await page.waitForTimeout(3000); // Wait for form submission and modal close
	
	// Verify modal is closed
	const passwordModal = page.locator('#settings-user-profile-password-modal');
	await expect(passwordModal).not.toBeVisible({ timeout: 10000 });
	
	console.log('✓ Password change form submitted successfully');
}

/**
 * Utility function to logout using sidebar
 * COMPONENT: Sidebar.svelte
 * ELEMENT: sidebar-logout-button (line 862)
 */
async function performLogout(page: Page) {
	console.log('Performing logout...');
	
	// Click on avatar to open slide-up menu
	const avatarTrigger = page.locator('#sidebar-avatar-trigger');
	await expect(avatarTrigger).toBeVisible({ timeout: 10000 });
	await avatarTrigger.click();
	await page.waitForTimeout(1000);
	
	// Wait for slide-up menu to appear
	const slideUpMenu = page.locator('#sidebar-slide-up-menu');
	await expect(slideUpMenu).toBeVisible({ timeout: 10000 });
	
	// Click logout button using the specific ID
	const logoutButton = page.locator('#sidebar-logout-button');
	await expect(logoutButton).toBeVisible({ timeout: 10000 });
	await logoutButton.click();
	
	// Wait for redirect to login page
	await page.waitForTimeout(2000);
	await expect(page).toHaveURL('/login');
	
	console.log('✓ Logout completed successfully');
}

/**
 * Utility function to attempt login with given credentials
 * COMPONENT: Login page
 * ELEMENTS: username and password inputs, submit button
 */
async function attemptLogin(page: Page, username: string, password: string): Promise<boolean> {
	console.log(`Attempting login with username: ${username}`);
	
	// Ensure we're on login page
	await page.goto('/login');
	await page.waitForTimeout(1000);
	
	// Fill login form
	await page.fill('input[name="username"]', username);
	await page.fill('input[name="password"]', password);
	await page.waitForTimeout(1000);
	
	// Submit form
	await page.click('button[type="submit"]');
	await page.waitForTimeout(3000);
	
	// Check if login was successful by checking URL
	const currentUrl = page.url();
	const loginSuccessful = !currentUrl.includes('/login');
	
	console.log(`Login attempt result: ${loginSuccessful ? 'SUCCESS' : 'FAILED'}`);
	return loginSuccessful;
}

test.describe('Account Settings Password Change', () => {
	test('should complete full password change workflow with authentication verification and reversion', async ({ page }) => {
		// Step 1: Authentication and navigation to account settings
		await performLoginWithRedirectHandling(page);
		await navigateToAccountSettings(page);
		
		// Step 2: Open password change modal
		await openPasswordChangeModal(page);
		
		// Step 3: Fill password change form with test password
		await fillPasswordChangeForm(page, ORIGINAL_PASSWORD, TEST_PASSWORD);
		
		// Step 4: Submit password change form
		await submitPasswordChangeForm(page);
		
		// Step 5: Logout to test password change
		await performLogout(page);
		
		// Step 6: Test that old password fails
		const oldPasswordWorks = await attemptLogin(page, USERNAME, ORIGINAL_PASSWORD);
		expect(oldPasswordWorks).toBe(false);
		console.log('✓ Old password correctly rejected');
		
		// Step 7: Test that new password succeeds
		const newPasswordWorks = await attemptLogin(page, USERNAME, TEST_PASSWORD);
		expect(newPasswordWorks).toBe(true);
		console.log('✓ New password correctly accepted');
		
		// Step 8: Round-trip testing - revert password to original
		console.log('\n--- Performing round-trip testing ---');
		await navigateToAccountSettings(page);
		await openPasswordChangeModal(page);
		await fillPasswordChangeForm(page, TEST_PASSWORD, ORIGINAL_PASSWORD);
		await submitPasswordChangeForm(page);
		
		// Step 9: Logout and verify original password works again
		await performLogout(page);
		const originalPasswordRestored = await attemptLogin(page, USERNAME, ORIGINAL_PASSWORD);
		expect(originalPasswordRestored).toBe(true);
		console.log('✓ Original password restored successfully');
		
		// Step 10: Verify test password no longer works
		await performLogout(page);
		const testPasswordStillWorks = await attemptLogin(page, USERNAME, TEST_PASSWORD);
		expect(testPasswordStillWorks).toBe(false);
		console.log('✓ Test password correctly disabled');
		
		console.log('\n🎉 Account settings password change workflow completed successfully!');
	});
});
